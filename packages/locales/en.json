{"userEmailNotFound": {"other": "user with this email doesnt exist"}, "invalidField": {"other": "invalid {{.field}}"}, "invalidSession": {"other": "invalid session"}, "userEmailAlreadyExists": {"other": "user with this email already exists"}, "tokenOrOtpRequired": {"other": "token or otp required"}, "password": {"other": "password"}, "session": {"other": "session"}, "otp": {"other": "OTP"}, "token": {"other": "Token"}, "emailVerification": {"other": "Email verified Successfully"}, "emailAlreadyVerified": {"other": "Email already verified"}, "emailVerificationTimer": {"other": "verification email already sent. Please wait for 2 minutes before sending another"}, "verificationEmail": {"other": "Verification email sent"}, "fieldNotFound": {"other": "{{.field}} not found"}, "user": {"other": "User"}, "passwordReset": {"other": "Password reset successfully"}, "passwordUpdate": {"other": "Password updated successfully"}, "loggedOut": {"other": "Logged out successfully"}, "campaign": {"other": "Campaign"}, "unauthorizedBannerAdd": {"other": "you are not allowed to add banner to this campaign"}, "banner": {"other": "Banner"}, "unauthorizedBannerDelete": {"other": "you are not allowed to delete this banner"}, "bannerDeleted": {"other": "Banner deleted successfully"}, "unauthorizedBannerUpdate": {"other": "you are not allowed to update this banner"}, "bannerUpdated": {"other": "Banner updated successfully"}, "comment": {"other": "Comment"}, "unauthorizedCommentDelete": {"other": "you are not allowed to delete comment to this campaign"}, "commentDeleted": {"other": "Comment deleted successfully"}, "unauthorizedCommentUpdate": {"other": "you are not allowed to update comment to this campaign"}, "commentUpdated": {"other": "Comment updated successfully"}, "alreadyLiked": {"other": "you have already liked this campaign"}, "likeAdded": {"other": "Like added successfully"}, "like": {"other": "Like"}, "likeRemoved": {"other": "Like removed successfully"}, "category": {"other": "Category"}, "unauthorizedCampaignDelete": {"other": "you are not allowed to delete this campaign"}, "unauthorizedCampaignUpdate": {"other": "you are not allowed to update this campaign"}, "campaignDeleted": {"other": "Campaign deleted successfully"}, "campaignUpdated": {"other": "Campaign updated successfully"}, "variant": {"other": "<PERSON><PERSON><PERSON>"}, "unauthorizedSubView": {"other": "you are not authorized to view subs of this campaign"}, "unauthorizedVariantAdd": {"other": "you are not authorized to add campaign variant"}, "unauthorizedVariantDelete": {"other": "you are not authorized to delete campaign variant"}, "variantDeleted": {"other": "Campaign variant deleted successfully"}, "unauthorizedVariantUpdate": {"other": "you are not authorized to update campaign variant"}, "variantUpdated": {"other": "Campaign variant updated successfully"}, "categoryUpdated": {"other": "Category updated successfully"}, "categoryDeleted": {"other": "Category deleted successfully"}, "creatorSubIndexAlreadyExists": {"other": "creator subscription this position already exists"}, "creatorSub": {"other": "creator subscription"}, "unauthorizedCreatorSubDelete": {"other": "you are not authorized to delete creator subscription"}, "creatorSubDeleted": {"other": "Creator subscription deleted successfully"}, "unauthorizedCreatorSubUpdate": {"other": "you are not authorized to update creator subscription"}, "creatorSubUpdated": {"other": "Creator subscription updated successfully"}, "creatorUserSub": {"other": "creator user subscription"}, "unauthorizedToPerformAction": {"other": "you are not authorized to perform this action"}, "creatorUserSubDeleted": {"other": "Creator user subscription deleted successfully"}, "creatorUserSubUpdated": {"other": "Creator user subscription updated successfully"}, "event": {"other": "Event"}, "eventNotApproved": {"other": "Event not approved"}, "alreadyParticipatedInEvent": {"other": "you have already participated in this event"}, "eventPost": {"other": "Event post"}, "eventPostDeleted": {"other": "Event post deleted successfully"}, "eventPostUpdated": {"other": "Event post updated successfully"}, "alreadyVotedForEventPost": {"other": "you have already voted for this event post"}, "voteAdded": {"other": "Vote added successfully"}, "voteRemoved": {"other": "Vote removed successfully"}, "eventPostVote": {"other": "Event post vote"}, "eventStatusUpdated": {"other": "Event status updated successfully"}, "eventDeleted": {"other": "Event deleted successfully"}, "eventUpdated": {"other": "Event updated successfully"}, "FAQ": {"other": "FAQ"}, "FAQDeleted": {"other": "FAQ deleted successfully"}, "FAQUpdated": {"other": "FAQ updated successfully"}, "FAQStatusUpdated": {"other": "FAQ status updated successfully"}, "post": {"other": "Post"}, "postCommentDeleted": {"other": "Post comment deleted successfully"}, "postCommentUpdated": {"other": "Post comment updated successfully"}, "alreadyLikedPost": {"other": "you have already liked this post"}, "creatorIdRequired": {"other": "Creator id required"}, "postDeleted": {"other": "Post deleted successfully"}, "postUpdated": {"other": "Post updated successfully"}, "fileRequired": {"other": "File required"}, "invalidFilePath": {"other": "Invalid file path"}, "file": {"other": "File"}, "invalidFileType": {"other": "Invalid file type"}, "atLeastOneRequiredForTransaction": {"other": "At least one of creator subscription id or campaign variant id is required"}, "onlyOneRequiredForTransaction": {"other": "Only one of creator subscription id or campaign variant id is required"}, "alreadySubscribed": {"other": "you have already subscribed to this creator"}, "alreadySubscribedToCampaign": {"other": "you have already subscribed to this campaign"}, "subLimitReached": {"other": "subscription limit reached for campaign variant"}, "transaction": {"other": "Transaction"}, "userGuideAlreadyExists": {"other": "user guide already exists"}, "userGuideUpdated": {"other": "User guide updated successfully"}, "userGuide": {"other": "User guide"}, "userGuideDeleted": {"other": "User guide deleted successfully"}, "userDeleted": {"other": "User deleted successfully"}, "userBanned": {"other": "User banned successfully"}, "creatorProfileAlreadyExist": {"other": "Creator profile already exists"}, "creatorProfile": {"other": "Creator profile"}, "creatorProfileUpdated": {"other": "Creator profile updated successfully"}, "creatorProfileDeleted": {"other": "Creator profile deleted successfully"}, "notAppliedForCreator": {"other": "User has not applied for creator access"}, "creatorProfileVerified": {"other": "Creator profile verified successfully"}, "userAlreadyCreator": {"other": "User is already a creator"}, "userAlreadyAppliedForCreator": {"other": "User has already applied for creator access"}, "appliedForCreator": {"other": "Applied for creator access successfully"}, "creatorAccessRequest": {"other": "Creator access request"}, "creatorAccessDenied": {"other": "Creator access denied successfully"}, "creatorAccessAlreadyApproved": {"other": "Creator access already approved"}, "creatorAccessRequestUpdated": {"other": "Creator access request updated successfully"}, "notification": {"other": "Notification"}, "notificationRead": {"other": "Notification marked as read successfully"}, "notificationUnread": {"other": "Notification marked as unread successfully"}, "cardAdded": {"other": "Card added successfully"}, "card": {"other": "Card"}, "cardDeleted": {"other": "Card deleted successfully"}, "usernameAlreadyTaken": {"other": "Username already taken"}, "adminCannotApplyForCreator": {"other": "Admin cannot apply for creator access"}, "invalidTag": {"other": "Invalid tag"}, "creatorSubscriptionIndexExists": {"other": "Plan with this index already exists"}}