// Code generated by sqlc. DO NOT EDIT.
// versions:
//   sqlc v1.29.0
// source: users.sql

package usersqueries

import (
	"context"
	"time"
)

const banUser = `-- name: BanUser :exec
UPDATE users
SET is_banned = true
WHERE id = $1
`

func (q *Queries) BanUser(ctx context.Context, id int64) error {
	_, err := q.db.Exec(ctx, banUser, id)
	return err
}

const createUser = `-- name: CreateUser :one
INSERT INTO users (full_name ,email ,date_of_birth , email_verified, role) VALUES ($1, $2, $3, $4, $5) RETURNING id, full_name, email, date_of_birth, image, email_verified, role, is_banned, ban_reason, ban_expires_at, created_at, updated_at, deleted_at
`

type CreateUserParams struct {
	FullName      string
	Email         string
	DateOfBirth   *time.Time
	EmailVerified bool
	Role          UserRole
}

func (q *Queries) CreateUser(ctx context.Context, arg CreateUserParams) (User, error) {
	row := q.db.QueryRow(ctx, createUser,
		arg.FullName,
		arg.Email,
		arg.DateOfBirth,
		arg.EmailVerified,
		arg.Role,
	)
	var i User
	err := row.Scan(
		&i.ID,
		&i.FullName,
		&i.Email,
		&i.DateOfBirth,
		&i.Image,
		&i.EmailVerified,
		&i.Role,
		&i.IsBanned,
		&i.BanReason,
		&i.BanExpiresAt,
		&i.CreatedAt,
		&i.UpdatedAt,
		&i.DeletedAt,
	)
	return i, err
}

const deleteUser = `-- name: DeleteUser :exec
UPDATE users
SET deleted_at = CURRENT_TIMESTAMP
WHERE id = $1 RETURNING id, full_name, email, date_of_birth, image, email_verified, role, is_banned, ban_reason, ban_expires_at, created_at, updated_at, deleted_at
`

func (q *Queries) DeleteUser(ctx context.Context, id int64) error {
	_, err := q.db.Exec(ctx, deleteUser, id)
	return err
}

const getUser = `-- name: GetUser :one
SELECT u.id, u.full_name, u.email, u.date_of_birth, u.image, u.email_verified, u.role, u.is_banned, u.ban_reason, u.ban_expires_at, u.created_at, u.updated_at, u.deleted_at, vp.id as vtuber_id FROM users u  LEFT JOIN  vtuber_profiles vp ON u.id = vp.user_id WHERE u.id=$1
`

type GetUserRow struct {
	ID            int64
	FullName      string
	Email         string
	DateOfBirth   *time.Time
	Image         *string
	EmailVerified bool
	Role          UserRole
	IsBanned      bool
	BanReason     *string
	BanExpiresAt  *time.Time
	CreatedAt     time.Time
	UpdatedAt     time.Time
	DeletedAt     *time.Time
	VtuberID      *int64
}

func (q *Queries) GetUser(ctx context.Context, id int64) (GetUserRow, error) {
	row := q.db.QueryRow(ctx, getUser, id)
	var i GetUserRow
	err := row.Scan(
		&i.ID,
		&i.FullName,
		&i.Email,
		&i.DateOfBirth,
		&i.Image,
		&i.EmailVerified,
		&i.Role,
		&i.IsBanned,
		&i.BanReason,
		&i.BanExpiresAt,
		&i.CreatedAt,
		&i.UpdatedAt,
		&i.DeletedAt,
		&i.VtuberID,
	)
	return i, err
}

const getUserByEmail = `-- name: GetUserByEmail :one
SELECT id, full_name, email, date_of_birth, image, email_verified, role, is_banned, ban_reason, ban_expires_at, created_at, updated_at, deleted_at FROM users WHERE email = $1 AND deleted_at IS NULL
`

func (q *Queries) GetUserByEmail(ctx context.Context, email string) (User, error) {
	row := q.db.QueryRow(ctx, getUserByEmail, email)
	var i User
	err := row.Scan(
		&i.ID,
		&i.FullName,
		&i.Email,
		&i.DateOfBirth,
		&i.Image,
		&i.EmailVerified,
		&i.Role,
		&i.IsBanned,
		&i.BanReason,
		&i.BanExpiresAt,
		&i.CreatedAt,
		&i.UpdatedAt,
		&i.DeletedAt,
	)
	return i, err
}

const getUserByID = `-- name: GetUserByID :one
SELECT id, full_name, email, date_of_birth, image, email_verified, role, is_banned, ban_reason, ban_expires_at, created_at, updated_at, deleted_at
FROM users
WHERE id = $1 AND deleted_at IS NULL
`

func (q *Queries) GetUserByID(ctx context.Context, id int64) (User, error) {
	row := q.db.QueryRow(ctx, getUserByID, id)
	var i User
	err := row.Scan(
		&i.ID,
		&i.FullName,
		&i.Email,
		&i.DateOfBirth,
		&i.Image,
		&i.EmailVerified,
		&i.Role,
		&i.IsBanned,
		&i.BanReason,
		&i.BanExpiresAt,
		&i.CreatedAt,
		&i.UpdatedAt,
		&i.DeletedAt,
	)
	return i, err
}

const getUserById = `-- name: GetUserById :one
SELECT id, full_name, email, date_of_birth, image, email_verified, role, is_banned, ban_reason, ban_expires_at, created_at, updated_at, deleted_at FROM users WHERE id = $1
`

func (q *Queries) GetUserById(ctx context.Context, id int64) (User, error) {
	row := q.db.QueryRow(ctx, getUserById, id)
	var i User
	err := row.Scan(
		&i.ID,
		&i.FullName,
		&i.Email,
		&i.DateOfBirth,
		&i.Image,
		&i.EmailVerified,
		&i.Role,
		&i.IsBanned,
		&i.BanReason,
		&i.BanExpiresAt,
		&i.CreatedAt,
		&i.UpdatedAt,
		&i.DeletedAt,
	)
	return i, err
}

const getUserName = `-- name: GetUserName :one
SELECT full_name FROM users
WHERE id = $1
`

func (q *Queries) GetUserName(ctx context.Context, id int64) (string, error) {
	row := q.db.QueryRow(ctx, getUserName, id)
	var full_name string
	err := row.Scan(&full_name)
	return full_name, err
}

const listDeletedUsers = `-- name: ListDeletedUsers :many
WITH FILTERED AS (SELECT id, full_name, email, date_of_birth, image, email_verified, role, is_banned, ban_reason, ban_expires_at, created_at, updated_at, deleted_at
FROM users
WHERE email LIKE COALESCE('%' || $5 || '%', email) AND deleted_at IS NOT NULL
),
COUNTED AS(
    SELECT COUNT(*) AS total FROM FILTERED
)
SELECT f.id, f.full_name, f.email, f.date_of_birth, f.image, f.email_verified, f.role, f.is_banned, f.ban_reason, f.ban_expires_at, f.created_at, f.updated_at, f.deleted_at, c.total FROM FILTERED f, COUNTED c
ORDER BY
    CASE WHEN $1::TEXT = 'id' AND $2::TEXT = 'ASC' THEN id END ASC,
    CASE WHEN $1::TEXT = 'id' AND $2::TEXT = 'DESC' THEN id END DESC,
    CASE WHEN $1::TEXT = 'full_name' AND $2::TEXT = 'ASC' THEN full_name END ASC,
    CASE WHEN $1::TEXT = 'full_name' AND $2::TEXT = 'DESC' THEN full_name END DESC,
    CASE WHEN $1::TEXT = 'email' AND $2::TEXT = 'ASC' THEN email END ASC,
    CASE WHEN $1::TEXT = 'email' AND $2::TEXT = 'DESC' THEN email END DESC,
    CASE WHEN $1::TEXT = 'created_at' AND $2::TEXT = 'ASC' THEN created_at END ASC,
    CASE WHEN $1::TEXT = 'created_at' AND $2::TEXT = 'DESC' THEN created_at END DESC,
    CASE WHEN $1::TEXT = 'updated_at' AND $2::TEXT = 'ASC' THEN updated_at END ASC,
    CASE WHEN $1::TEXT = 'updated_at' AND $2::TEXT = 'DESC' THEN updated_at END DESC
LIMIT $4 OFFSET $3
`

type ListDeletedUsersParams struct {
	Sort   string
	Order  string
	Offset int32
	Limit  int32
	Email  *string
}

type ListDeletedUsersRow struct {
	ID            int64
	FullName      string
	Email         string
	DateOfBirth   *time.Time
	Image         *string
	EmailVerified bool
	Role          UserRole
	IsBanned      bool
	BanReason     *string
	BanExpiresAt  *time.Time
	CreatedAt     time.Time
	UpdatedAt     time.Time
	DeletedAt     *time.Time
	Total         int64
}

func (q *Queries) ListDeletedUsers(ctx context.Context, arg ListDeletedUsersParams) ([]ListDeletedUsersRow, error) {
	rows, err := q.db.Query(ctx, listDeletedUsers,
		arg.Sort,
		arg.Order,
		arg.Offset,
		arg.Limit,
		arg.Email,
	)
	if err != nil {
		return nil, err
	}
	defer rows.Close()
	var items []ListDeletedUsersRow
	for rows.Next() {
		var i ListDeletedUsersRow
		if err := rows.Scan(
			&i.ID,
			&i.FullName,
			&i.Email,
			&i.DateOfBirth,
			&i.Image,
			&i.EmailVerified,
			&i.Role,
			&i.IsBanned,
			&i.BanReason,
			&i.BanExpiresAt,
			&i.CreatedAt,
			&i.UpdatedAt,
			&i.DeletedAt,
			&i.Total,
		); err != nil {
			return nil, err
		}
		items = append(items, i)
	}
	if err := rows.Err(); err != nil {
		return nil, err
	}
	return items, nil
}

const listOfBannedUsers = `-- name: ListOfBannedUsers :many

WITH FILTERED AS (SELECT id, full_name, email, date_of_birth, image, email_verified, role, is_banned, ban_reason, ban_expires_at, created_at, updated_at, deleted_at
FROM users
WHERE email LIKE COALESCE('%' || $5 || '%', email) AND deleted_at IS NULL AND is_banned = true
),
COUNTED AS(
    SELECT COUNT(*) AS total FROM FILTERED
)
SELECT f.id, f.full_name, f.email, f.date_of_birth, f.image, f.email_verified, f.role, f.is_banned, f.ban_reason, f.ban_expires_at, f.created_at, f.updated_at, f.deleted_at, c.total FROM FILTERED f, COUNTED c
ORDER BY
    CASE WHEN $1::TEXT = 'id' AND $2::TEXT = 'ASC' THEN id END ASC,
    CASE WHEN $1::TEXT = 'id' AND $2::TEXT = 'DESC' THEN id END DESC,
    CASE WHEN $1::TEXT = 'full_name' AND $2::TEXT = 'ASC' THEN full_name END ASC,
    CASE WHEN $1::TEXT = 'full_name' AND $2::TEXT = 'DESC' THEN full_name END DESC,
    CASE WHEN $1::TEXT = 'email' AND $2::TEXT = 'ASC' THEN email END ASC,
    CASE WHEN $1::TEXT = 'email' AND $2::TEXT = 'DESC' THEN email END DESC,
    CASE WHEN $1::TEXT = 'created_at' AND $2::TEXT = 'ASC' THEN created_at END ASC,
    CASE WHEN $1::TEXT = 'created_at' AND $2::TEXT = 'DESC' THEN created_at END DESC,
    CASE WHEN $1::TEXT = 'updated_at' AND $2::TEXT = 'ASC' THEN updated_at END ASC,
    CASE WHEN $1::TEXT = 'updated_at' AND $2::TEXT = 'DESC' THEN updated_at END DESC
LIMIT $4 OFFSET $3
`

type ListOfBannedUsersParams struct {
	Sort   string
	Order  string
	Offset int32
	Limit  int32
	Email  *string
}

type ListOfBannedUsersRow struct {
	ID            int64
	FullName      string
	Email         string
	DateOfBirth   *time.Time
	Image         *string
	EmailVerified bool
	Role          UserRole
	IsBanned      bool
	BanReason     *string
	BanExpiresAt  *time.Time
	CreatedAt     time.Time
	UpdatedAt     time.Time
	DeletedAt     *time.Time
	Total         int64
}

func (q *Queries) ListOfBannedUsers(ctx context.Context, arg ListOfBannedUsersParams) ([]ListOfBannedUsersRow, error) {
	rows, err := q.db.Query(ctx, listOfBannedUsers,
		arg.Sort,
		arg.Order,
		arg.Offset,
		arg.Limit,
		arg.Email,
	)
	if err != nil {
		return nil, err
	}
	defer rows.Close()
	var items []ListOfBannedUsersRow
	for rows.Next() {
		var i ListOfBannedUsersRow
		if err := rows.Scan(
			&i.ID,
			&i.FullName,
			&i.Email,
			&i.DateOfBirth,
			&i.Image,
			&i.EmailVerified,
			&i.Role,
			&i.IsBanned,
			&i.BanReason,
			&i.BanExpiresAt,
			&i.CreatedAt,
			&i.UpdatedAt,
			&i.DeletedAt,
			&i.Total,
		); err != nil {
			return nil, err
		}
		items = append(items, i)
	}
	if err := rows.Err(); err != nil {
		return nil, err
	}
	return items, nil
}

const listUsers = `-- name: ListUsers :many
WITH FILTERED AS (SELECT id, full_name, email, date_of_birth, image, email_verified, role, is_banned, ban_reason, ban_expires_at, created_at, updated_at, deleted_at
FROM users
WHERE email LIKE COALESCE('%' || $5 || '%', email) AND deleted_at IS NULL AND is_banned = false
),
COUNTED AS(
    SELECT COUNT(*) AS total FROM FILTERED
)
SELECT f.id, f.full_name, f.email, f.date_of_birth, f.image, f.email_verified, f.role, f.is_banned, f.ban_reason, f.ban_expires_at, f.created_at, f.updated_at, f.deleted_at, c.total FROM FILTERED f, COUNTED c
ORDER BY
    CASE WHEN $1::TEXT = 'id' AND $2::TEXT = 'ASC' THEN id END ASC,
    CASE WHEN $1::TEXT = 'id' AND $2::TEXT = 'DESC' THEN id END DESC,
    CASE WHEN $1::TEXT = 'full_name' AND $2::TEXT = 'ASC' THEN full_name END ASC,
    CASE WHEN $1::TEXT = 'full_name' AND $2::TEXT = 'DESC' THEN full_name END DESC,
    CASE WHEN $1::TEXT = 'email' AND $2::TEXT = 'ASC' THEN email END ASC,
    CASE WHEN $1::TEXT = 'email' AND $2::TEXT = 'DESC' THEN email END DESC,
    CASE WHEN $1::TEXT = 'created_at' AND $2::TEXT = 'ASC' THEN created_at END ASC,
    CASE WHEN $1::TEXT = 'created_at' AND $2::TEXT = 'DESC' THEN created_at END DESC,
    CASE WHEN $1::TEXT = 'updated_at' AND $2::TEXT = 'ASC' THEN updated_at END ASC,
    CASE WHEN $1::TEXT = 'updated_at' AND $2::TEXT = 'DESC' THEN updated_at END DESC
LIMIT $4 OFFSET $3
`

type ListUsersParams struct {
	Sort   string
	Order  string
	Offset int32
	Limit  int32
	Email  *string
}

type ListUsersRow struct {
	ID            int64
	FullName      string
	Email         string
	DateOfBirth   *time.Time
	Image         *string
	EmailVerified bool
	Role          UserRole
	IsBanned      bool
	BanReason     *string
	BanExpiresAt  *time.Time
	CreatedAt     time.Time
	UpdatedAt     time.Time
	DeletedAt     *time.Time
	Total         int64
}

func (q *Queries) ListUsers(ctx context.Context, arg ListUsersParams) ([]ListUsersRow, error) {
	rows, err := q.db.Query(ctx, listUsers,
		arg.Sort,
		arg.Order,
		arg.Offset,
		arg.Limit,
		arg.Email,
	)
	if err != nil {
		return nil, err
	}
	defer rows.Close()
	var items []ListUsersRow
	for rows.Next() {
		var i ListUsersRow
		if err := rows.Scan(
			&i.ID,
			&i.FullName,
			&i.Email,
			&i.DateOfBirth,
			&i.Image,
			&i.EmailVerified,
			&i.Role,
			&i.IsBanned,
			&i.BanReason,
			&i.BanExpiresAt,
			&i.CreatedAt,
			&i.UpdatedAt,
			&i.DeletedAt,
			&i.Total,
		); err != nil {
			return nil, err
		}
		items = append(items, i)
	}
	if err := rows.Err(); err != nil {
		return nil, err
	}
	return items, nil
}

const updateUser = `-- name: UpdateUser :one
UPDATE users
SET full_name = $1,
    image     = $2
WHERE id = $3 RETURNING id, full_name, email, date_of_birth, image, email_verified, role, is_banned, ban_reason, ban_expires_at, created_at, updated_at, deleted_at
`

type UpdateUserParams struct {
	FullName string
	Image    *string
	ID       int64
}

func (q *Queries) UpdateUser(ctx context.Context, arg UpdateUserParams) (User, error) {
	row := q.db.QueryRow(ctx, updateUser, arg.FullName, arg.Image, arg.ID)
	var i User
	err := row.Scan(
		&i.ID,
		&i.FullName,
		&i.Email,
		&i.DateOfBirth,
		&i.Image,
		&i.EmailVerified,
		&i.Role,
		&i.IsBanned,
		&i.BanReason,
		&i.BanExpiresAt,
		&i.CreatedAt,
		&i.UpdatedAt,
		&i.DeletedAt,
	)
	return i, err
}

const updateUserDetails = `-- name: UpdateUserDetails :one
UPDATE users
SET full_name = $1, date_of_birth = $2, updated_at = CURRENT_TIMESTAMP
WHERE id = $3 RETURNING id, full_name, email, date_of_birth, image, email_verified, role, is_banned, ban_reason, ban_expires_at, created_at, updated_at, deleted_at
`

type UpdateUserDetailsParams struct {
	FullName    string
	DateOfBirth *time.Time
	ID          int64
}

func (q *Queries) UpdateUserDetails(ctx context.Context, arg UpdateUserDetailsParams) (User, error) {
	row := q.db.QueryRow(ctx, updateUserDetails, arg.FullName, arg.DateOfBirth, arg.ID)
	var i User
	err := row.Scan(
		&i.ID,
		&i.FullName,
		&i.Email,
		&i.DateOfBirth,
		&i.Image,
		&i.EmailVerified,
		&i.Role,
		&i.IsBanned,
		&i.BanReason,
		&i.BanExpiresAt,
		&i.CreatedAt,
		&i.UpdatedAt,
		&i.DeletedAt,
	)
	return i, err
}

const updateUserEmail = `-- name: UpdateUserEmail :one
UPDATE users
SET email = $1, email_verified = $2, updated_at = CURRENT_TIMESTAMP
WHERE id = $3 RETURNING id, full_name, email, date_of_birth, image, email_verified, role, is_banned, ban_reason, ban_expires_at, created_at, updated_at, deleted_at
`

type UpdateUserEmailParams struct {
	Email         string
	EmailVerified bool
	ID            int64
}

func (q *Queries) UpdateUserEmail(ctx context.Context, arg UpdateUserEmailParams) (User, error) {
	row := q.db.QueryRow(ctx, updateUserEmail, arg.Email, arg.EmailVerified, arg.ID)
	var i User
	err := row.Scan(
		&i.ID,
		&i.FullName,
		&i.Email,
		&i.DateOfBirth,
		&i.Image,
		&i.EmailVerified,
		&i.Role,
		&i.IsBanned,
		&i.BanReason,
		&i.BanExpiresAt,
		&i.CreatedAt,
		&i.UpdatedAt,
		&i.DeletedAt,
	)
	return i, err
}

const updateUserImage = `-- name: UpdateUserImage :one
UPDATE users SET image = $1 RETURNING image
`

func (q *Queries) UpdateUserImage(ctx context.Context, image *string) (*string, error) {
	row := q.db.QueryRow(ctx, updateUserImage, image)
	err := row.Scan(&image)
	return image, err
}
