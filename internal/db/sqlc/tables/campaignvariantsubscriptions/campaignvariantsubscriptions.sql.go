// Code generated by sqlc. DO NOT EDIT.
// versions:
//   sqlc v1.29.0
// source: campaignvariantsubscriptions.sql

package campaignvariantsubscriptionsqueries

import (
	"context"
)

const addCampaignVariantSubscription = `-- name: AddCampaignVariantSubscription :one
INSERT INTO campaign_variant_subscriptions (user_id, campaign_variant_id, vtuber_id, price,campaign_id, comment)
VALUES ($1,$2,$3,$4,$5, $6) RETURNING id, user_id, campaign_variant_id, campaign_id, vtuber_id, price, comment, created_at, updated_at
`

type AddCampaignVariantSubscriptionParams struct {
	UserID            int64
	CampaignVariantID int64
	VtuberID          int64
	Price             int32
	CampaignID        int64
	Comment           *string
}

func (q *Queries) AddCampaignVariantSubscription(ctx context.Context, arg AddCampaignVariantSubscriptionParams) (CampaignVariantSubscription, error) {
	row := q.db.QueryRow(ctx, addCampaignVariantSubscription,
		arg.UserID,
		arg.CampaignVariantID,
		arg.VtuberID,
		arg.Price,
		arg.CampaignID,
		arg.Comment,
	)
	var i CampaignVariantSubscription
	err := row.Scan(
		&i.ID,
		&i.UserID,
		&i.CampaignVariantID,
		&i.CampaignID,
		&i.VtuberID,
		&i.Price,
		&i.Comment,
		&i.CreatedAt,
		&i.UpdatedAt,
	)
	return i, err
}

const deleteCampaignVariantSubscription = `-- name: DeleteCampaignVariantSubscription :exec
DELETE FROM campaign_variant_subscriptions WHERE id=$1
`

func (q *Queries) DeleteCampaignVariantSubscription(ctx context.Context, id int64) error {
	_, err := q.db.Exec(ctx, deleteCampaignVariantSubscription, id)
	return err
}

const getCampaignVariantSubscriptionByUserIdAndCampaignVariantId = `-- name: GetCampaignVariantSubscriptionByUserIdAndCampaignVariantId :one
SELECT id, user_id, campaign_variant_id, campaign_id, vtuber_id, price, comment, created_at, updated_at FROM campaign_variant_subscriptions WHERE user_id=$1 AND campaign_variant_id=$2
`

type GetCampaignVariantSubscriptionByUserIdAndCampaignVariantIdParams struct {
	UserID            int64
	CampaignVariantID int64
}

func (q *Queries) GetCampaignVariantSubscriptionByUserIdAndCampaignVariantId(ctx context.Context, arg GetCampaignVariantSubscriptionByUserIdAndCampaignVariantIdParams) (CampaignVariantSubscription, error) {
	row := q.db.QueryRow(ctx, getCampaignVariantSubscriptionByUserIdAndCampaignVariantId, arg.UserID, arg.CampaignVariantID)
	var i CampaignVariantSubscription
	err := row.Scan(
		&i.ID,
		&i.UserID,
		&i.CampaignVariantID,
		&i.CampaignID,
		&i.VtuberID,
		&i.Price,
		&i.Comment,
		&i.CreatedAt,
		&i.UpdatedAt,
	)
	return i, err
}

const getCampaignVariantSubscriptionCount = `-- name: GetCampaignVariantSubscriptionCount :one
SELECT COUNT(*) FROM campaign_variant_subscriptions WHERE campaign_variant_id=$1
`

func (q *Queries) GetCampaignVariantSubscriptionCount(ctx context.Context, campaignVariantID int64) (int64, error) {
	row := q.db.QueryRow(ctx, getCampaignVariantSubscriptionCount, campaignVariantID)
	var count int64
	err := row.Scan(&count)
	return count, err
}

const popularCampaign = `-- name: PopularCampaign :one
SELECT SUM(cvs.price) as price, cvs.campaign_id
FROM campaign_variant_subscriptions cvs 
INNER JOIN campaigns c ON c.id = cvs.campaign_id 
WHERE c.deleted_at IS NULL
GROUP BY cvs.campaign_id ORDER BY price DESC LIMIT 1
`

type PopularCampaignRow struct {
	Price      int64
	CampaignID int64
}

func (q *Queries) PopularCampaign(ctx context.Context) (PopularCampaignRow, error) {
	row := q.db.QueryRow(ctx, popularCampaign)
	var i PopularCampaignRow
	err := row.Scan(&i.Price, &i.CampaignID)
	return i, err
}
