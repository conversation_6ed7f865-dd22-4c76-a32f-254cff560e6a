// Code generated by sqlc. DO NOT EDIT.
// versions:
//   sqlc v1.29.0

package accountsqueries

import (
	"database/sql/driver"
	"fmt"
	"time"

	"github.com/jackc/pgx/v5/pgtype"
)

type UserRole string

const (
	UserRoleUser   UserRole = "user"
	UserRoleAdmin  UserRole = "admin"
	UserRoleVtuber UserRole = "vtuber"
)

func (e *UserRole) Scan(src interface{}) error {
	switch s := src.(type) {
	case []byte:
		*e = UserRole(s)
	case string:
		*e = UserRole(s)
	default:
		return fmt.Errorf("unsupported scan type for UserRole: %T", src)
	}
	return nil
}

type NullUserRole struct {
	UserRole UserRole
	Valid    bool // Valid is true if UserRole is not NULL
}

// Scan implements the Scanner interface.
func (ns *NullUserRole) Scan(value interface{}) error {
	if value == nil {
		ns.UserRole, ns.Valid = "", false
		return nil
	}
	ns.Valid = true
	return ns.UserRole.Scan(value)
}

// Value implements the driver Valuer interface.
func (ns NullUserRole) Value() (driver.Value, error) {
	if !ns.Valid {
		return nil, nil
	}
	return string(ns.UserRole), nil
}

type Account struct {
	ID                  int64
	AccountID           string
	ProviderID          string
	UserID              int64
	AccessToken         *string
	RefreshToken        *string
	IDToken             *string
	AccessTokenExpires  pgtype.Timestamptz
	RefreshTokenExpires pgtype.Timestamptz
	Scope               *string
	Password            *string
	CreatedAt           pgtype.Timestamptz
	UpdatedAt           pgtype.Timestamptz
	LastThreePassword   []byte
}

type User struct {
	ID            int64
	FullName      string
	Email         string
	DateOfBirth   *time.Time
	Image         *string
	EmailVerified bool
	Role          UserRole
	IsBanned      bool
	BanReason     *string
	BanExpiresAt  *time.Time
	CreatedAt     time.Time
	UpdatedAt     time.Time
	DeletedAt     *time.Time
}
