// Code generated by sqlc. DO NOT EDIT.
// versions:
//   sqlc v1.29.0
// source: campaigns.sql

package campaignsqueries

import (
	"context"
	"time"
)

const createCampaign = `-- name: CreateCampaign :one
INSERT INTO campaigns (name, description, start_date, end_date, total_budget, vtuber_id, thumbnail,
                       short_description, promotional_message, social_media_links,slug)
VALUES ($1, $2, $3, $4, $5, $6, $7, $8, $9, $10, $11) RETURNING id, slug, name, description, short_description, thumbnail, start_date, end_date, total_budget, promotional_message, social_media_links, vtuber_id, created_at, updated_at, deleted_at
`

type CreateCampaignParams struct {
	Name               string
	Description        string
	StartDate          time.Time
	EndDate            time.Time
	TotalBudget        int32
	VtuberID           int64
	Thumbnail          string
	ShortDescription   string
	PromotionalMessage string
	SocialMediaLinks   []byte
	Slug               string
}

func (q *Queries) CreateCampaign(ctx context.Context, arg CreateCampaignParams) (Campaign, error) {
	row := q.db.QueryRow(ctx, createCampaign,
		arg.Name,
		arg.Description,
		arg.StartDate,
		arg.EndDate,
		arg.TotalBudget,
		arg.VtuberID,
		arg.Thumbnail,
		arg.ShortDescription,
		arg.PromotionalMessage,
		arg.SocialMediaLinks,
		arg.Slug,
	)
	var i Campaign
	err := row.Scan(
		&i.ID,
		&i.Slug,
		&i.Name,
		&i.Description,
		&i.ShortDescription,
		&i.Thumbnail,
		&i.StartDate,
		&i.EndDate,
		&i.TotalBudget,
		&i.PromotionalMessage,
		&i.SocialMediaLinks,
		&i.VtuberID,
		&i.CreatedAt,
		&i.UpdatedAt,
		&i.DeletedAt,
	)
	return i, err
}

const deleteCampaign = `-- name: DeleteCampaign :exec
UPDATE campaigns
SET deleted_at = CURRENT_TIMESTAMP
WHERE id = $1
`

func (q *Queries) DeleteCampaign(ctx context.Context, id int64) error {
	_, err := q.db.Exec(ctx, deleteCampaign, id)
	return err
}

const getAllCampaigns = `-- name: GetAllCampaigns :many
WITH FILTERED AS (SELECT c.id, c.slug, c.name, c.description, c.short_description, c.thumbnail, c.start_date, c.end_date, c.total_budget, c.promotional_message, c.social_media_links, c.vtuber_id, c.created_at, c.updated_at, c.deleted_at, vp.display_name, (SELECT COALESCE(SUM(price), 0)::INT FROM campaign_variant_subscriptions WHERE c.id = campaign_variant_subscriptions.campaign_id) as total_raised,
       CASE 
        WHEN COUNT(cc.category_id) = 0 THEN ARRAY[]::BIGINT[]
        ELSE ARRAY_AGG(cc.category_id)::BIGINT[]
    END as category_ids
                  FROM campaigns c 
                  INNER JOIN vtuber_profiles vp ON vp.id = c.vtuber_id  
                     LEFT JOIN campaign_categories cc ON c.id = cc.campaign_id
                   WHERE c.deleted_at IS NULL AND ($5::BIGINT IS NULL OR cc.category_id = $5::BIGINT)
                   GROUP BY c.id,  vp.display_name , vp.image, vp.user_id, vp.furigana, vp.banner_image, vp.social_media_links, vp.description, vp.created_at
                   ),
     COUNTED AS (SELECT COUNT(*) AS total
                 FROM FILTERED)
SELECT f.id, f.slug, f.name, f.description, f.short_description, f.thumbnail, f.start_date, f.end_date, f.total_budget, f.promotional_message, f.social_media_links, f.vtuber_id, f.created_at, f.updated_at, f.deleted_at, f.display_name, f.total_raised, f.category_ids, c.total
FROM FILTERED f,
     COUNTED c
ORDER BY CASE WHEN $1::TEXT = 'id' AND $2::TEXT = 'ASC' THEN id END ASC,
      CASE WHEN $1::TEXT = 'id' AND $2::TEXT = 'DESC' THEN id
END
DESC,
      CASE WHEN $1::TEXT = 'name' AND $2::TEXT = 'ASC' THEN name
END
ASC,
      CASE WHEN $1::TEXT = 'name' AND $2::TEXT = 'DESC' THEN name
END
DESC,
      CASE WHEN $1::TEXT = 'total_budget' AND $2::TEXT = 'ASC' THEN total_budget
END
ASC,
      CASE WHEN $1::TEXT = 'total_budget' AND $2::TEXT = 'DESC' THEN total_budget
END
DESC,
      CASE WHEN $1::TEXT = 'created_at' AND $2::TEXT = 'ASC' THEN created_at
END
ASC,
      CASE WHEN $1::TEXT = 'created_at' AND $2::TEXT = 'DESC' THEN created_at
END
DESC,
      CASE WHEN $1::TEXT = 'updated_at' AND $2::TEXT = 'ASC' THEN updated_at
END
ASC,
      CASE WHEN $1::TEXT = 'updated_at' AND $2::TEXT = 'DESC' THEN updated_at
END
DESC
   LIMIT $4 OFFSET $3
`

type GetAllCampaignsParams struct {
	Sort       string
	Order      string
	Offset     int32
	Limit      int32
	CategoryID *int64
}

type GetAllCampaignsRow struct {
	ID                 int64
	Slug               string
	Name               string
	Description        string
	ShortDescription   string
	Thumbnail          string
	StartDate          time.Time
	EndDate            time.Time
	TotalBudget        int32
	PromotionalMessage string
	SocialMediaLinks   []byte
	VtuberID           int64
	CreatedAt          time.Time
	UpdatedAt          time.Time
	DeletedAt          *time.Time
	DisplayName        string
	TotalRaised        int32
	CategoryIds        []int64
	Total              int64
}

func (q *Queries) GetAllCampaigns(ctx context.Context, arg GetAllCampaignsParams) ([]GetAllCampaignsRow, error) {
	rows, err := q.db.Query(ctx, getAllCampaigns,
		arg.Sort,
		arg.Order,
		arg.Offset,
		arg.Limit,
		arg.CategoryID,
	)
	if err != nil {
		return nil, err
	}
	defer rows.Close()
	var items []GetAllCampaignsRow
	for rows.Next() {
		var i GetAllCampaignsRow
		if err := rows.Scan(
			&i.ID,
			&i.Slug,
			&i.Name,
			&i.Description,
			&i.ShortDescription,
			&i.Thumbnail,
			&i.StartDate,
			&i.EndDate,
			&i.TotalBudget,
			&i.PromotionalMessage,
			&i.SocialMediaLinks,
			&i.VtuberID,
			&i.CreatedAt,
			&i.UpdatedAt,
			&i.DeletedAt,
			&i.DisplayName,
			&i.TotalRaised,
			&i.CategoryIds,
			&i.Total,
		); err != nil {
			return nil, err
		}
		items = append(items, i)
	}
	if err := rows.Err(); err != nil {
		return nil, err
	}
	return items, nil
}

const getCampaignById = `-- name: GetCampaignById :one
SELECT c.id, c.slug, c.name, c.description, c.short_description, c.thumbnail, c.start_date, c.end_date, c.total_budget, c.promotional_message, c.social_media_links, c.vtuber_id, c.created_at, c.updated_at, c.deleted_at,
       vp.display_name   as vtuber_name,
       vp.image          as vtuber_image,
       vp.user_id        as vtuber_user_id,
       vp.furigana       as vtuber_furigana,
       vp.banner_image   as vtuber_banner_image,
       vp.social_media_links as vtuber_social_media_links,
       vp.description    as vtuber_description,
       vp.created_at as vtuber_created_at,
         (SELECT COALESCE(SUM(price), 0)::INT FROM campaign_variant_subscriptions WHERE campaign_id::TEXT = $1::TEXT) as total_raised,
      CASE 
        WHEN COUNT(cc.category_id) = 0 THEN ARRAY[]::BIGINT[]
        ELSE ARRAY_AGG(cc.category_id)::BIGINT[]
    END as category_ids
FROM campaigns c
         INNER JOIN vtuber_profiles vp ON c.vtuber_id = vp.id
         LEFT JOIN campaign_categories cc ON c.id = cc.campaign_id
WHERE (c.id::TEXT = $1 OR c.slug = $1::TEXT)
  AND c.deleted_at IS NULL 
  GROUP BY c.id, vp.display_name , vp.image, vp.user_id, vp.furigana, vp.banner_image, vp.social_media_links, vp.description, vp.created_at
`

type GetCampaignByIdRow struct {
	ID                     int64
	Slug                   string
	Name                   string
	Description            string
	ShortDescription       string
	Thumbnail              string
	StartDate              time.Time
	EndDate                time.Time
	TotalBudget            int32
	PromotionalMessage     string
	SocialMediaLinks       []byte
	VtuberID               int64
	CreatedAt              time.Time
	UpdatedAt              time.Time
	DeletedAt              *time.Time
	VtuberName             string
	VtuberImage            *string
	VtuberUserID           int64
	VtuberFurigana         string
	VtuberBannerImage      *string
	VtuberSocialMediaLinks []byte
	VtuberDescription      *string
	VtuberCreatedAt        time.Time
	TotalRaised            int32
	CategoryIds            []int64
}

func (q *Queries) GetCampaignById(ctx context.Context, id string) (GetCampaignByIdRow, error) {
	row := q.db.QueryRow(ctx, getCampaignById, id)
	var i GetCampaignByIdRow
	err := row.Scan(
		&i.ID,
		&i.Slug,
		&i.Name,
		&i.Description,
		&i.ShortDescription,
		&i.Thumbnail,
		&i.StartDate,
		&i.EndDate,
		&i.TotalBudget,
		&i.PromotionalMessage,
		&i.SocialMediaLinks,
		&i.VtuberID,
		&i.CreatedAt,
		&i.UpdatedAt,
		&i.DeletedAt,
		&i.VtuberName,
		&i.VtuberImage,
		&i.VtuberUserID,
		&i.VtuberFurigana,
		&i.VtuberBannerImage,
		&i.VtuberSocialMediaLinks,
		&i.VtuberDescription,
		&i.VtuberCreatedAt,
		&i.TotalRaised,
		&i.CategoryIds,
	)
	return i, err
}

const getCampaignBySlug = `-- name: GetCampaignBySlug :one
SELECT id, slug, name, description, short_description, thumbnail, start_date, end_date, total_budget, promotional_message, social_media_links, vtuber_id, created_at, updated_at, deleted_at FROM campaigns WHERE slug = $1
`

func (q *Queries) GetCampaignBySlug(ctx context.Context, slug string) (Campaign, error) {
	row := q.db.QueryRow(ctx, getCampaignBySlug, slug)
	var i Campaign
	err := row.Scan(
		&i.ID,
		&i.Slug,
		&i.Name,
		&i.Description,
		&i.ShortDescription,
		&i.Thumbnail,
		&i.StartDate,
		&i.EndDate,
		&i.TotalBudget,
		&i.PromotionalMessage,
		&i.SocialMediaLinks,
		&i.VtuberID,
		&i.CreatedAt,
		&i.UpdatedAt,
		&i.DeletedAt,
	)
	return i, err
}

const getCampaignSubscriberComments = `-- name: GetCampaignSubscriberComments :many
WITH FILTERED AS (SELECT cvs.user_id, u.full_name, u.image, cvs.created_at, cvs.id, cvs.comment FROM campaign_variant_subscriptions cvs INNER JOIN users u ON cvs.user_id = u.id 
WHERE cvs.campaign_id = $1 AND cvs.comment IS NOT NULL),
 COUNTED AS (SELECT COUNT(*) AS total
                 FROM FILTERED)
SELECT f.user_id, f.full_name, f.image, f.created_at, f.id, f.comment, c.total
FROM FILTERED f,
     COUNTED c
ORDER BY created_at DESC
   LIMIT $3 OFFSET $2
`

type GetCampaignSubscriberCommentsParams struct {
	CampaignID int64
	Offset     int32
	Limit      int32
}

type GetCampaignSubscriberCommentsRow struct {
	UserID    int64
	FullName  string
	Image     *string
	CreatedAt time.Time
	ID        int64
	Comment   *string
	Total     int64
}

func (q *Queries) GetCampaignSubscriberComments(ctx context.Context, arg GetCampaignSubscriberCommentsParams) ([]GetCampaignSubscriberCommentsRow, error) {
	rows, err := q.db.Query(ctx, getCampaignSubscriberComments, arg.CampaignID, arg.Offset, arg.Limit)
	if err != nil {
		return nil, err
	}
	defer rows.Close()
	var items []GetCampaignSubscriberCommentsRow
	for rows.Next() {
		var i GetCampaignSubscriberCommentsRow
		if err := rows.Scan(
			&i.UserID,
			&i.FullName,
			&i.Image,
			&i.CreatedAt,
			&i.ID,
			&i.Comment,
			&i.Total,
		); err != nil {
			return nil, err
		}
		items = append(items, i)
	}
	if err := rows.Err(); err != nil {
		return nil, err
	}
	return items, nil
}

const getCampaignSubscribers = `-- name: GetCampaignSubscribers :many
WITH FILTERED AS (SELECT (cvs.user_id), u.full_name, u.image, cvs.created_at, cvs.comment FROM campaign_variant_subscriptions cvs
INNER JOIN users u ON u.id = cvs.user_id WHERE cvs.campaign_id = $1),
COUNTED AS (
SELECT COUNT(*) AS total FROM FILTERED
)
SELECT f.user_id, f.full_name, f.image, f.created_at, f.comment, c.total FROM FILTERED f, COUNTED c
ORDER BY
    CASE WHEN $2::TEXT = 'created_at' AND $3::TEXT = 'ASC' THEN created_at END,
    CASE WHEN $2::TEXT = 'created_at' AND $3::TEXT = 'DESC' THEN created_at END DESC
LIMIT $5 OFFSET $4
`

type GetCampaignSubscribersParams struct {
	CampaignID int64
	Sort       string
	Order      string
	Offset     int32
	Limit      int32
}

type GetCampaignSubscribersRow struct {
	UserID    int64
	FullName  string
	Image     *string
	CreatedAt time.Time
	Comment   *string
	Total     int64
}

func (q *Queries) GetCampaignSubscribers(ctx context.Context, arg GetCampaignSubscribersParams) ([]GetCampaignSubscribersRow, error) {
	rows, err := q.db.Query(ctx, getCampaignSubscribers,
		arg.CampaignID,
		arg.Sort,
		arg.Order,
		arg.Offset,
		arg.Limit,
	)
	if err != nil {
		return nil, err
	}
	defer rows.Close()
	var items []GetCampaignSubscribersRow
	for rows.Next() {
		var i GetCampaignSubscribersRow
		if err := rows.Scan(
			&i.UserID,
			&i.FullName,
			&i.Image,
			&i.CreatedAt,
			&i.Comment,
			&i.Total,
		); err != nil {
			return nil, err
		}
		items = append(items, i)
	}
	if err := rows.Err(); err != nil {
		return nil, err
	}
	return items, nil
}

const getCampaignsByVtuber = `-- name: GetCampaignsByVtuber :many
WITH FILTERED AS (SELECT c.id, c.slug, c.name, c.description, c.short_description, c.thumbnail, c.start_date, c.end_date, c.total_budget, c.promotional_message, c.social_media_links, c.vtuber_id, c.created_at, c.updated_at, c.deleted_at, (SELECT COALESCE(SUM(price), 0)::INT FROM campaign_variant_subscriptions WHERE c.id = campaign_variant_subscriptions.campaign_id) as total_raised,
      CASE 
        WHEN COUNT(cc.category_id) = 0 THEN ARRAY[]::BIGINT[]
        ELSE ARRAY_AGG(cc.category_id)::BIGINT[]
    END as category_ids
                  FROM campaigns c
                    LEFT JOIN campaign_categories cc ON c.id = cc.campaign_id
                  WHERE c.vtuber_id = $1
                    AND c.deleted_at IS NULL
                    GROUP BY c.id
                    ),
     COUNTED AS (SELECT COUNT(*) AS total
                 FROM FILTERED)
SELECT f.id, f.slug, f.name, f.description, f.short_description, f.thumbnail, f.start_date, f.end_date, f.total_budget, f.promotional_message, f.social_media_links, f.vtuber_id, f.created_at, f.updated_at, f.deleted_at, f.total_raised, f.category_ids, c.total
FROM FILTERED f,
     COUNTED c
ORDER BY CASE WHEN $2::TEXT = 'id' AND $3::TEXT = 'ASC' THEN id END ASC,
      CASE WHEN $2::TEXT = 'id' AND $3::TEXT = 'DESC' THEN id
END
DESC,
      CASE WHEN $2::TEXT = 'name' AND $3::TEXT = 'ASC' THEN name
END
ASC,
      CASE WHEN $2::TEXT = 'name' AND $3::TEXT = 'DESC' THEN name
END
DESC,
      CASE WHEN $2::TEXT = 'total_budget' AND $3::TEXT = 'ASC' THEN total_budget
END
ASC,
      CASE WHEN $2::TEXT = 'total_budget' AND $3::TEXT = 'DESC' THEN total_budget
END
DESC,
      CASE WHEN $2::TEXT = 'created_at' AND $3::TEXT = 'ASC' THEN created_at
END
ASC,
      CASE WHEN $2::TEXT = 'created_at' AND $3::TEXT = 'DESC' THEN created_at
END
DESC,
      CASE WHEN $2::TEXT = 'updated_at' AND $3::TEXT = 'ASC' THEN updated_at
END
ASC,
      CASE WHEN $2::TEXT = 'updated_at' AND $3::TEXT = 'DESC' THEN updated_at
END
DESC
   LIMIT $5 OFFSET $4
`

type GetCampaignsByVtuberParams struct {
	VtuberID int64
	Sort     string
	Order    string
	Offset   int32
	Limit    int32
}

type GetCampaignsByVtuberRow struct {
	ID                 int64
	Slug               string
	Name               string
	Description        string
	ShortDescription   string
	Thumbnail          string
	StartDate          time.Time
	EndDate            time.Time
	TotalBudget        int32
	PromotionalMessage string
	SocialMediaLinks   []byte
	VtuberID           int64
	CreatedAt          time.Time
	UpdatedAt          time.Time
	DeletedAt          *time.Time
	TotalRaised        int32
	CategoryIds        []int64
	Total              int64
}

func (q *Queries) GetCampaignsByVtuber(ctx context.Context, arg GetCampaignsByVtuberParams) ([]GetCampaignsByVtuberRow, error) {
	rows, err := q.db.Query(ctx, getCampaignsByVtuber,
		arg.VtuberID,
		arg.Sort,
		arg.Order,
		arg.Offset,
		arg.Limit,
	)
	if err != nil {
		return nil, err
	}
	defer rows.Close()
	var items []GetCampaignsByVtuberRow
	for rows.Next() {
		var i GetCampaignsByVtuberRow
		if err := rows.Scan(
			&i.ID,
			&i.Slug,
			&i.Name,
			&i.Description,
			&i.ShortDescription,
			&i.Thumbnail,
			&i.StartDate,
			&i.EndDate,
			&i.TotalBudget,
			&i.PromotionalMessage,
			&i.SocialMediaLinks,
			&i.VtuberID,
			&i.CreatedAt,
			&i.UpdatedAt,
			&i.DeletedAt,
			&i.TotalRaised,
			&i.CategoryIds,
			&i.Total,
		); err != nil {
			return nil, err
		}
		items = append(items, i)
	}
	if err := rows.Err(); err != nil {
		return nil, err
	}
	return items, nil
}

const getMyCampaignNames = `-- name: GetMyCampaignNames :many
SELECT c.name, c.id FROM campaigns c WHERE c.vtuber_id = $1
`

type GetMyCampaignNamesRow struct {
	Name string
	ID   int64
}

func (q *Queries) GetMyCampaignNames(ctx context.Context, vtuberID int64) ([]GetMyCampaignNamesRow, error) {
	rows, err := q.db.Query(ctx, getMyCampaignNames, vtuberID)
	if err != nil {
		return nil, err
	}
	defer rows.Close()
	var items []GetMyCampaignNamesRow
	for rows.Next() {
		var i GetMyCampaignNamesRow
		if err := rows.Scan(&i.Name, &i.ID); err != nil {
			return nil, err
		}
		items = append(items, i)
	}
	if err := rows.Err(); err != nil {
		return nil, err
	}
	return items, nil
}

const getMySupportedCampaigns = `-- name: GetMySupportedCampaigns :many
   WITH FILTERED AS (SELECT DISTINCT(cvs.campaign_id), c.name, c.short_description, c.created_at, cvs.created_at as supported_created_at, c.total_budget, c.start_date, c.end_date, c.vtuber_id, c.promotional_message,c.social_media_links, c.slug, 
(SELECT COALESCE(SUM(price), 0)::INT FROM campaign_variant_subscriptions WHERE c.id = campaign_variant_subscriptions.campaign_id) as total_raised,
 c.thumbnail FROM campaign_variant_subscriptions cvs INNER JOIN campaigns c ON c.id = cvs.campaign_id WHERE cvs.user_id = $1),
     COUNTED AS (SELECT COUNT(*) AS total
                 FROM FILTERED)
SELECT f.campaign_id, f.name, f.short_description, f.created_at, f.supported_created_at, f.total_budget, f.start_date, f.end_date, f.vtuber_id, f.promotional_message, f.social_media_links, f.slug, f.total_raised, f.thumbnail, c.total
FROM FILTERED f,
     COUNTED c
ORDER BY supported_created_at DESC
   LIMIT $3 OFFSET $2
`

type GetMySupportedCampaignsParams struct {
	UserID int64
	Offset int32
	Limit  int32
}

type GetMySupportedCampaignsRow struct {
	CampaignID         int64
	Name               string
	ShortDescription   string
	CreatedAt          time.Time
	SupportedCreatedAt time.Time
	TotalBudget        int32
	StartDate          time.Time
	EndDate            time.Time
	VtuberID           int64
	PromotionalMessage string
	SocialMediaLinks   []byte
	Slug               string
	TotalRaised        int32
	Thumbnail          string
	Total              int64
}

func (q *Queries) GetMySupportedCampaigns(ctx context.Context, arg GetMySupportedCampaignsParams) ([]GetMySupportedCampaignsRow, error) {
	rows, err := q.db.Query(ctx, getMySupportedCampaigns, arg.UserID, arg.Offset, arg.Limit)
	if err != nil {
		return nil, err
	}
	defer rows.Close()
	var items []GetMySupportedCampaignsRow
	for rows.Next() {
		var i GetMySupportedCampaignsRow
		if err := rows.Scan(
			&i.CampaignID,
			&i.Name,
			&i.ShortDescription,
			&i.CreatedAt,
			&i.SupportedCreatedAt,
			&i.TotalBudget,
			&i.StartDate,
			&i.EndDate,
			&i.VtuberID,
			&i.PromotionalMessage,
			&i.SocialMediaLinks,
			&i.Slug,
			&i.TotalRaised,
			&i.Thumbnail,
			&i.Total,
		); err != nil {
			return nil, err
		}
		items = append(items, i)
	}
	if err := rows.Err(); err != nil {
		return nil, err
	}
	return items, nil
}

const getRandomCampaign = `-- name: GetRandomCampaign :one
SELECT c.id, c.slug, c.name, c.description, c.short_description, c.thumbnail, c.start_date, c.end_date, c.total_budget, c.promotional_message, c.social_media_links, c.vtuber_id, c.created_at, c.updated_at, c.deleted_at,
       vp.display_name   as vtuber_name,
       vp.image          as vtuber_image,
       vp.user_id        as vtuber_user_id,
       vp.furigana       as vtuber_furigana,
       vp.banner_image   as vtuber_banner_image,
       vp.social_media_links as vtuber_social_media_links,
       vp.description    as vtuber_description,
       vp.created_at as vtuber_created_at,
         (SELECT COALESCE(SUM(price), 0)::INT FROM campaign_variant_subscriptions WHERE campaign_id = c.id) as total_raised,
         CASE 
        WHEN COUNT(cc.category_id) = 0 THEN ARRAY[]::BIGINT[]
        ELSE ARRAY_AGG(cc.category_id)::BIGINT[]
    END as category_ids
FROM campaigns c
         INNER JOIN vtuber_profiles vp ON c.vtuber_id = vp.id
         LEFT JOIN campaign_categories cc ON c.id = cc.campaign_id
WHERE c.deleted_at IS NULL ORDER BY RANDOM() LIMIT 1
`

type GetRandomCampaignRow struct {
	ID                     int64
	Slug                   string
	Name                   string
	Description            string
	ShortDescription       string
	Thumbnail              string
	StartDate              time.Time
	EndDate                time.Time
	TotalBudget            int32
	PromotionalMessage     string
	SocialMediaLinks       []byte
	VtuberID               int64
	CreatedAt              time.Time
	UpdatedAt              time.Time
	DeletedAt              *time.Time
	VtuberName             string
	VtuberImage            *string
	VtuberUserID           int64
	VtuberFurigana         string
	VtuberBannerImage      *string
	VtuberSocialMediaLinks []byte
	VtuberDescription      *string
	VtuberCreatedAt        time.Time
	TotalRaised            int32
	CategoryIds            []int64
}

func (q *Queries) GetRandomCampaign(ctx context.Context) (GetRandomCampaignRow, error) {
	row := q.db.QueryRow(ctx, getRandomCampaign)
	var i GetRandomCampaignRow
	err := row.Scan(
		&i.ID,
		&i.Slug,
		&i.Name,
		&i.Description,
		&i.ShortDescription,
		&i.Thumbnail,
		&i.StartDate,
		&i.EndDate,
		&i.TotalBudget,
		&i.PromotionalMessage,
		&i.SocialMediaLinks,
		&i.VtuberID,
		&i.CreatedAt,
		&i.UpdatedAt,
		&i.DeletedAt,
		&i.VtuberName,
		&i.VtuberImage,
		&i.VtuberUserID,
		&i.VtuberFurigana,
		&i.VtuberBannerImage,
		&i.VtuberSocialMediaLinks,
		&i.VtuberDescription,
		&i.VtuberCreatedAt,
		&i.TotalRaised,
		&i.CategoryIds,
	)
	return i, err
}

const getRelatedCampaign = `-- name: GetRelatedCampaign :many
SELECT c.id, c.slug, c.name, c.description, c.short_description, c.thumbnail, c.start_date, c.end_date, c.total_budget, c.promotional_message, c.social_media_links, c.vtuber_id, c.created_at, c.updated_at, c.deleted_at, vp.display_name, (SELECT COALESCE(SUM(price), 0)::INT FROM campaign_variant_subscriptions WHERE c.id = campaign_variant_subscriptions.campaign_id) as total_raised,
      CASE 
        WHEN COUNT(cc.category_id) = 0 THEN ARRAY[]::BIGINT[]
        ELSE ARRAY_AGG(cc.category_id)::BIGINT[]
    END as category_ids
                  FROM campaigns c 
                  INNER JOIN vtuber_profiles vp ON vp.id = c.vtuber_id  
                     LEFT JOIN campaign_categories cc ON c.id = cc.campaign_id
                   WHERE c.deleted_at IS NULL AND cc.category_id = ANY($1)
                   GROUP BY c.id,  vp.display_name , vp.image, vp.user_id, vp.furigana, vp.banner_image, vp.social_media_links, vp.description, vp.created_at
               ORDER BY c.created_at DESC LIMIT 4
`

type GetRelatedCampaignRow struct {
	ID                 int64
	Slug               string
	Name               string
	Description        string
	ShortDescription   string
	Thumbnail          string
	StartDate          time.Time
	EndDate            time.Time
	TotalBudget        int32
	PromotionalMessage string
	SocialMediaLinks   []byte
	VtuberID           int64
	CreatedAt          time.Time
	UpdatedAt          time.Time
	DeletedAt          *time.Time
	DisplayName        string
	TotalRaised        int32
	CategoryIds        []int64
}

func (q *Queries) GetRelatedCampaign(ctx context.Context, ids []int64) ([]GetRelatedCampaignRow, error) {
	rows, err := q.db.Query(ctx, getRelatedCampaign, ids)
	if err != nil {
		return nil, err
	}
	defer rows.Close()
	var items []GetRelatedCampaignRow
	for rows.Next() {
		var i GetRelatedCampaignRow
		if err := rows.Scan(
			&i.ID,
			&i.Slug,
			&i.Name,
			&i.Description,
			&i.ShortDescription,
			&i.Thumbnail,
			&i.StartDate,
			&i.EndDate,
			&i.TotalBudget,
			&i.PromotionalMessage,
			&i.SocialMediaLinks,
			&i.VtuberID,
			&i.CreatedAt,
			&i.UpdatedAt,
			&i.DeletedAt,
			&i.DisplayName,
			&i.TotalRaised,
			&i.CategoryIds,
		); err != nil {
			return nil, err
		}
		items = append(items, i)
	}
	if err := rows.Err(); err != nil {
		return nil, err
	}
	return items, nil
}

const updateCampaign = `-- name: UpdateCampaign :exec
UPDATE campaigns
SET name              = $1,
    description       = $2,
    start_date        = $3,
    end_date          = $4,
    total_budget      = $5,
    thumbnail         = $6,
    short_description = $7,
    promotional_message = $8,
    social_media_links = $9
WHERE id = $10
`

type UpdateCampaignParams struct {
	Name               string
	Description        string
	StartDate          time.Time
	EndDate            time.Time
	TotalBudget        int32
	Thumbnail          string
	ShortDescription   string
	PromotionalMessage string
	SocialMediaLinks   []byte
	ID                 int64
}

func (q *Queries) UpdateCampaign(ctx context.Context, arg UpdateCampaignParams) error {
	_, err := q.db.Exec(ctx, updateCampaign,
		arg.Name,
		arg.Description,
		arg.StartDate,
		arg.EndDate,
		arg.TotalBudget,
		arg.Thumbnail,
		arg.ShortDescription,
		arg.PromotionalMessage,
		arg.SocialMediaLinks,
		arg.ID,
	)
	return err
}

const userLikedCampaign = `-- name: UserLikedCampaign :one
SELECT count(id)
from favorite_campaigns
WHERE campaign_id = $1
  AND user_id = $2
`

type UserLikedCampaignParams struct {
	CampaignID int64
	UserID     int64
}

func (q *Queries) UserLikedCampaign(ctx context.Context, arg UserLikedCampaignParams) (int64, error) {
	row := q.db.QueryRow(ctx, userLikedCampaign, arg.CampaignID, arg.UserID)
	var count int64
	err := row.Scan(&count)
	return count, err
}
