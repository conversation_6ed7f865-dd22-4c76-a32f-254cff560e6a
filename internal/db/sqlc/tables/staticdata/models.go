// Code generated by sqlc. DO NOT EDIT.
// versions:
//   sqlc v1.29.0

package staticdataqueries

import (
	"database/sql/driver"
	"fmt"
	"time"
)

type Language string

const (
	LanguageEnUs Language = "en-us"
	LanguageJaJp Language = "ja-jp"
)

func (e *Language) Scan(src interface{}) error {
	switch s := src.(type) {
	case []byte:
		*e = Language(s)
	case string:
		*e = Language(s)
	default:
		return fmt.Errorf("unsupported scan type for Language: %T", src)
	}
	return nil
}

type NullLanguage struct {
	Language Language
	Valid    bool // Valid is true if Language is not NULL
}

// <PERSON>an implements the Scanner interface.
func (ns *NullLanguage) Scan(value interface{}) error {
	if value == nil {
		ns.Language, ns.Valid = "", false
		return nil
	}
	ns.Valid = true
	return ns.Language.Scan(value)
}

// Value implements the driver Valuer interface.
func (ns NullLanguage) Value() (driver.Value, error) {
	if !ns.Valid {
		return nil, nil
	}
	return string(ns.Language), nil
}

type StaticDatum struct {
	ID        int64
	Key       string
	Value     string
	Language  Language
	CreatedAt time.Time
	UpdatedAt time.Time
	Data      []byte
}
