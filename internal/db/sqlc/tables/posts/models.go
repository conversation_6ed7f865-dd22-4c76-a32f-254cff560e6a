// Code generated by sqlc. DO NOT EDIT.
// versions:
//   sqlc v1.29.0

package postsqueries

import (
	"time"
)

type Post struct {
	ID               int64
	Slug             string
	VtuberProfileID  int64
	MembershipOnly   bool
	Description      string
	ShortDescription string
	Title            string
	Name             string
	Media            *string
	MediaType        *string
	CategoryID       int64
	CreatedAt        time.Time
	UpdatedAt        time.Time
	DeletedAt        *time.Time
	CampaignID       *int64
}
