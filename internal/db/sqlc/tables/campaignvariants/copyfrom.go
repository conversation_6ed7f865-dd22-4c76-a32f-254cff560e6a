// Code generated by sqlc. DO NOT EDIT.
// versions:
//   sqlc v1.29.0
// source: copyfrom.go

package campaignvariantsqueries

import (
	"context"
)

// iteratorForInsertManyCampaignVariants implements pgx.CopyFromSource.
type iteratorForInsertManyCampaignVariants struct {
	rows                 []InsertManyCampaignVariantsParams
	skippedFirstNextCall bool
}

func (r *iteratorForInsertManyCampaignVariants) Next() bool {
	if len(r.rows) == 0 {
		return false
	}
	if !r.skippedFirstNextCall {
		r.skippedFirstNextCall = true
		return true
	}
	r.rows = r.rows[1:]
	return len(r.rows) > 0
}

func (r iteratorForInsertManyCampaignVariants) Values() ([]interface{}, error) {
	return []interface{}{
		r.rows[0].Description,
		r.rows[0].Price,
		r.rows[0].MaxSub,
		r.rows[0].Image,
		r.rows[0].CampaignID,
		r.rows[0].Title,
	}, nil
}

func (r iteratorForInsertManyCampaignVariants) Err() error {
	return nil
}

func (q *Queries) InsertManyCampaignVariants(ctx context.Context, arg []InsertManyCampaignVariantsParams) (int64, error) {
	return q.db.CopyFrom(ctx, []string{"campaign_variants"}, []string{"description", "price", "max_sub", "image", "campaign_id", "title"}, &iteratorForInsertManyCampaignVariants{rows: arg})
}
