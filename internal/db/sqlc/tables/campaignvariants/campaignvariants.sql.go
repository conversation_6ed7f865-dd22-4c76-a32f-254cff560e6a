// Code generated by sqlc. DO NOT EDIT.
// versions:
//   sqlc v1.29.0
// source: campaignvariants.sql

package campaignvariantsqueries

import (
	"context"
	"time"
)

const addCampaignVariant = `-- name: AddCampaignVariant :one
INSERT INTO campaign_variants (description, price, max_sub, image, campaign_id, title)
VALUES ($1, $2, $3, $4, $5, $6) RETURNING id, title, description, price, max_sub, image, campaign_id, created_at, updated_at, deleted_at
`

type AddCampaignVariantParams struct {
	Description string
	Price       int32
	MaxSub      int32
	Image       string
	CampaignID  int64
	Title       string
}

func (q *Queries) AddCampaignVariant(ctx context.Context, arg AddCampaignVariantParams) (CampaignVariant, error) {
	row := q.db.QueryRow(ctx, addCampaignVariant,
		arg.Description,
		arg.Price,
		arg.MaxSub,
		arg.Image,
		arg.CampaignID,
		arg.Title,
	)
	var i CampaignVariant
	err := row.Scan(
		&i.ID,
		&i.Title,
		&i.Description,
		&i.Price,
		&i.MaxSub,
		&i.Image,
		&i.CampaignID,
		&i.CreatedAt,
		&i.UpdatedAt,
		&i.DeletedAt,
	)
	return i, err
}

const deleteCampaignVariantById = `-- name: DeleteCampaignVariantById :exec
UPDATE campaign_variants SET deleted_at = CURRENT_TIMESTAMP WHERE id = $1
`

func (q *Queries) DeleteCampaignVariantById(ctx context.Context, id int64) error {
	_, err := q.db.Exec(ctx, deleteCampaignVariantById, id)
	return err
}

const getAllCampaignVariantsByCampaignId = `-- name: GetAllCampaignVariantsByCampaignId :many
SELECT 
  cv.id,
  cv.campaign_id,
  cv.price,
  cv.title,
  cv.max_sub,
  cv.description,
  cv.image,
  cv.created_at,
  COALESCE(t.count, 0) AS sub_count,
  CASE
    WHEN $1::BIGINT  IS NULL THEN FALSE
    WHEN EXISTS (
      SELECT 1
      FROM campaign_variant_subscriptions t2
      WHERE t2.campaign_variant_id = cv.id
        AND t2.user_id = $1::BIGINT
    ) THEN TRUE
    ELSE FALSE
  END AS has_subscribed

FROM campaign_variants cv
LEFT JOIN (
  SELECT campaign_variant_id, COUNT(id) AS count
  FROM campaign_variant_subscriptions
  GROUP BY campaign_variant_id
) t ON cv.id = t.campaign_variant_id
WHERE cv.campaign_id = $2::BIGINT AND cv.deleted_at IS NULL 
ORDER BY created_at ASC
`

type GetAllCampaignVariantsByCampaignIdParams struct {
	UserID     *int64
	CampaignID int64
}

type GetAllCampaignVariantsByCampaignIdRow struct {
	ID            int64
	CampaignID    int64
	Price         int32
	Title         string
	MaxSub        int32
	Description   string
	Image         string
	CreatedAt     time.Time
	SubCount      int64
	HasSubscribed bool
}

func (q *Queries) GetAllCampaignVariantsByCampaignId(ctx context.Context, arg GetAllCampaignVariantsByCampaignIdParams) ([]GetAllCampaignVariantsByCampaignIdRow, error) {
	rows, err := q.db.Query(ctx, getAllCampaignVariantsByCampaignId, arg.UserID, arg.CampaignID)
	if err != nil {
		return nil, err
	}
	defer rows.Close()
	var items []GetAllCampaignVariantsByCampaignIdRow
	for rows.Next() {
		var i GetAllCampaignVariantsByCampaignIdRow
		if err := rows.Scan(
			&i.ID,
			&i.CampaignID,
			&i.Price,
			&i.Title,
			&i.MaxSub,
			&i.Description,
			&i.Image,
			&i.CreatedAt,
			&i.SubCount,
			&i.HasSubscribed,
		); err != nil {
			return nil, err
		}
		items = append(items, i)
	}
	if err := rows.Err(); err != nil {
		return nil, err
	}
	return items, nil
}

const getCampaignVariantById = `-- name: GetCampaignVariantById :one
SELECT 
    cv.id, 
    cv.campaign_id, 
    cv.price, 
    cv.title,
    cv.max_sub,
    cv.description, 
    cv.image, 
    cv.created_at, 
    COALESCE(t.count , 0) AS sub_count
FROM campaign_variants cv
LEFT JOIN (
    SELECT 
        campaign_variant_id, 
        COUNT(id) AS count
    FROM campaign_variant_subscriptions
    GROUP BY campaign_variant_id
) t ON cv.id = t.campaign_variant_id
WHERE cv.id = $1 AND cv.deleted_at IS NULL
`

type GetCampaignVariantByIdRow struct {
	ID          int64
	CampaignID  int64
	Price       int32
	Title       string
	MaxSub      int32
	Description string
	Image       string
	CreatedAt   time.Time
	SubCount    int64
}

func (q *Queries) GetCampaignVariantById(ctx context.Context, id int64) (GetCampaignVariantByIdRow, error) {
	row := q.db.QueryRow(ctx, getCampaignVariantById, id)
	var i GetCampaignVariantByIdRow
	err := row.Scan(
		&i.ID,
		&i.CampaignID,
		&i.Price,
		&i.Title,
		&i.MaxSub,
		&i.Description,
		&i.Image,
		&i.CreatedAt,
		&i.SubCount,
	)
	return i, err
}

const getOneCampaignVariantById = `-- name: GetOneCampaignVariantById :one
SELECT id, title, description, price, max_sub, image, campaign_id, created_at, updated_at, deleted_at from campaign_variants WHERE id=$1
`

func (q *Queries) GetOneCampaignVariantById(ctx context.Context, id int64) (CampaignVariant, error) {
	row := q.db.QueryRow(ctx, getOneCampaignVariantById, id)
	var i CampaignVariant
	err := row.Scan(
		&i.ID,
		&i.Title,
		&i.Description,
		&i.Price,
		&i.MaxSub,
		&i.Image,
		&i.CampaignID,
		&i.CreatedAt,
		&i.UpdatedAt,
		&i.DeletedAt,
	)
	return i, err
}

const getSubsOfVariant = `-- name: GetSubsOfVariant :many
WITH FILTERED AS (SELECT t.campaign_variant_id, t.price, u.full_name as user_name, u.id as userId, u.image as user_image, t.created_at FROM campaign_variant_subscriptions t INNER JOIN users u ON t.user_id=u.id WHERE campaign_variant_id = $1),
     COUNTED AS(
         SELECT COUNT(*) AS total FROM FILTERED
     )
SELECT f.campaign_variant_id, f.price, f.user_name, f.userid, f.user_image, f.created_at, c.total FROM FILTERED f, COUNTED c
ORDER BY
    created_at DESC
LIMIT $3 OFFSET $2
`

type GetSubsOfVariantParams struct {
	CampaignVariantID int64
	Offset            int32
	Limit             int32
}

type GetSubsOfVariantRow struct {
	CampaignVariantID int64
	Price             int32
	UserName          string
	Userid            int64
	UserImage         *string
	CreatedAt         time.Time
	Total             int64
}

func (q *Queries) GetSubsOfVariant(ctx context.Context, arg GetSubsOfVariantParams) ([]GetSubsOfVariantRow, error) {
	rows, err := q.db.Query(ctx, getSubsOfVariant, arg.CampaignVariantID, arg.Offset, arg.Limit)
	if err != nil {
		return nil, err
	}
	defer rows.Close()
	var items []GetSubsOfVariantRow
	for rows.Next() {
		var i GetSubsOfVariantRow
		if err := rows.Scan(
			&i.CampaignVariantID,
			&i.Price,
			&i.UserName,
			&i.Userid,
			&i.UserImage,
			&i.CreatedAt,
			&i.Total,
		); err != nil {
			return nil, err
		}
		items = append(items, i)
	}
	if err := rows.Err(); err != nil {
		return nil, err
	}
	return items, nil
}

type InsertManyCampaignVariantsParams struct {
	Description string
	Price       int32
	MaxSub      int32
	Image       string
	CampaignID  int64
	Title       string
}

const updateCampaignVariantById = `-- name: UpdateCampaignVariantById :exec
UPDATE campaign_variants SET description = $1, price = $2, max_sub = $3, image = $4, title = $5 WHERE id = $6
`

type UpdateCampaignVariantByIdParams struct {
	Description string
	Price       int32
	MaxSub      int32
	Image       string
	Title       string
	ID          int64
}

func (q *Queries) UpdateCampaignVariantById(ctx context.Context, arg UpdateCampaignVariantByIdParams) error {
	_, err := q.db.Exec(ctx, updateCampaignVariantById,
		arg.Description,
		arg.Price,
		arg.MaxSub,
		arg.Image,
		arg.Title,
		arg.ID,
	)
	return err
}
