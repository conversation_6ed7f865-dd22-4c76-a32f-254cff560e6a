-- name: GetUserByEmail :one
SELECT * FROM users WHERE email = $1 AND deleted_at IS NULL;

-- name: GetUserById :one
SELECT * FROM users WHERE id = $1;

-- name: CreateUser :one
INSERT INTO users (full_name ,email ,date_of_birth , email_verified, role) VALUES ($1, $2, $3, $4, $5) RETURNING *;

-- name: UpdateUserDetails :one
UPDATE users
SET full_name = $1, date_of_birth = $2, updated_at = CURRENT_TIMESTAMP
WHERE id = $3 RETURNING *;

-- name: DeleteUser :exec
UPDATE users
SET deleted_at = CURRENT_TIMESTAMP
WHERE id = $1 RETURNING *;

-- name: UpdateUserEmail :one
UPDATE users
SET email = $1, email_verified = $2, updated_at = CURRENT_TIMESTAMP
WHERE id = $3 RETURNING *;

-- name: UpdateUserImage :one
UPDATE users SET image = $1 RETURNING image;

-- name: GetUserByID :one
SELECT *
FROM users
WHERE id = $1 AND deleted_at IS NULL;

-- name: GetUser :one
SELECT u.*, vp.id as vtuber_id FROM users u  LEFT JOIN  vtuber_profiles vp ON u.id = vp.user_id WHERE u.id=$1;
-- name: GetUserName :one
SELECT full_name FROM users
WHERE id = $1 ;

-- name: UpdateUser :one
UPDATE users
SET full_name = $1,
    image     = $2
WHERE id = $3 RETURNING *;

-- name: ListUsers :many
WITH FILTERED AS (SELECT *
FROM users
WHERE email LIKE COALESCE('%' || sqlc.narg('email') || '%', email) AND deleted_at IS NULL AND is_banned = false
),
COUNTED AS(
    SELECT COUNT(*) AS total FROM FILTERED
)
SELECT f.*, c.total FROM FILTERED f, COUNTED c
ORDER BY
    CASE WHEN sqlc.arg('sort')::TEXT = 'id' AND sqlc.arg('order')::TEXT = 'ASC' THEN id END ASC,
    CASE WHEN sqlc.arg('sort')::TEXT = 'id' AND sqlc.arg('order')::TEXT = 'DESC' THEN id END DESC,
    CASE WHEN sqlc.arg('sort')::TEXT = 'full_name' AND sqlc.arg('order')::TEXT = 'ASC' THEN full_name END ASC,
    CASE WHEN sqlc.arg('sort')::TEXT = 'full_name' AND sqlc.arg('order')::TEXT = 'DESC' THEN full_name END DESC,
    CASE WHEN sqlc.arg('sort')::TEXT = 'email' AND sqlc.arg('order')::TEXT = 'ASC' THEN email END ASC,
    CASE WHEN sqlc.arg('sort')::TEXT = 'email' AND sqlc.arg('order')::TEXT = 'DESC' THEN email END DESC,
    CASE WHEN sqlc.arg('sort')::TEXT = 'created_at' AND sqlc.arg('order')::TEXT = 'ASC' THEN created_at END ASC,
    CASE WHEN sqlc.arg('sort')::TEXT = 'created_at' AND sqlc.arg('order')::TEXT = 'DESC' THEN created_at END DESC,
    CASE WHEN sqlc.arg('sort')::TEXT = 'updated_at' AND sqlc.arg('order')::TEXT = 'ASC' THEN updated_at END ASC,
    CASE WHEN sqlc.arg('sort')::TEXT = 'updated_at' AND sqlc.arg('order')::TEXT = 'DESC' THEN updated_at END DESC
LIMIT sqlc.arg('limit') OFFSET sqlc.arg('offset');


-- name: ListDeletedUsers :many
WITH FILTERED AS (SELECT *
FROM users
WHERE email LIKE COALESCE('%' || sqlc.narg('email') || '%', email) AND deleted_at IS NOT NULL
),
COUNTED AS(
    SELECT COUNT(*) AS total FROM FILTERED
)
SELECT f.*, c.total FROM FILTERED f, COUNTED c
ORDER BY
    CASE WHEN sqlc.arg('sort')::TEXT = 'id' AND sqlc.arg('order')::TEXT = 'ASC' THEN id END ASC,
    CASE WHEN sqlc.arg('sort')::TEXT = 'id' AND sqlc.arg('order')::TEXT = 'DESC' THEN id END DESC,
    CASE WHEN sqlc.arg('sort')::TEXT = 'full_name' AND sqlc.arg('order')::TEXT = 'ASC' THEN full_name END ASC,
    CASE WHEN sqlc.arg('sort')::TEXT = 'full_name' AND sqlc.arg('order')::TEXT = 'DESC' THEN full_name END DESC,
    CASE WHEN sqlc.arg('sort')::TEXT = 'email' AND sqlc.arg('order')::TEXT = 'ASC' THEN email END ASC,
    CASE WHEN sqlc.arg('sort')::TEXT = 'email' AND sqlc.arg('order')::TEXT = 'DESC' THEN email END DESC,
    CASE WHEN sqlc.arg('sort')::TEXT = 'created_at' AND sqlc.arg('order')::TEXT = 'ASC' THEN created_at END ASC,
    CASE WHEN sqlc.arg('sort')::TEXT = 'created_at' AND sqlc.arg('order')::TEXT = 'DESC' THEN created_at END DESC,
    CASE WHEN sqlc.arg('sort')::TEXT = 'updated_at' AND sqlc.arg('order')::TEXT = 'ASC' THEN updated_at END ASC,
    CASE WHEN sqlc.arg('sort')::TEXT = 'updated_at' AND sqlc.arg('order')::TEXT = 'DESC' THEN updated_at END DESC
LIMIT sqlc.arg('limit') OFFSET sqlc.arg('offset');


-- name: BanUser :exec
UPDATE users
SET is_banned = true
WHERE id = $1;


-- name: ListOfBannedUsers :many

WITH FILTERED AS (SELECT *
FROM users
WHERE email LIKE COALESCE('%' || sqlc.narg('email') || '%', email) AND deleted_at IS NULL AND is_banned = true
),
COUNTED AS(
    SELECT COUNT(*) AS total FROM FILTERED
)
SELECT f.*, c.total FROM FILTERED f, COUNTED c
ORDER BY
    CASE WHEN sqlc.arg('sort')::TEXT = 'id' AND sqlc.arg('order')::TEXT = 'ASC' THEN id END ASC,
    CASE WHEN sqlc.arg('sort')::TEXT = 'id' AND sqlc.arg('order')::TEXT = 'DESC' THEN id END DESC,
    CASE WHEN sqlc.arg('sort')::TEXT = 'full_name' AND sqlc.arg('order')::TEXT = 'ASC' THEN full_name END ASC,
    CASE WHEN sqlc.arg('sort')::TEXT = 'full_name' AND sqlc.arg('order')::TEXT = 'DESC' THEN full_name END DESC,
    CASE WHEN sqlc.arg('sort')::TEXT = 'email' AND sqlc.arg('order')::TEXT = 'ASC' THEN email END ASC,
    CASE WHEN sqlc.arg('sort')::TEXT = 'email' AND sqlc.arg('order')::TEXT = 'DESC' THEN email END DESC,
    CASE WHEN sqlc.arg('sort')::TEXT = 'created_at' AND sqlc.arg('order')::TEXT = 'ASC' THEN created_at END ASC,
    CASE WHEN sqlc.arg('sort')::TEXT = 'created_at' AND sqlc.arg('order')::TEXT = 'DESC' THEN created_at END DESC,
    CASE WHEN sqlc.arg('sort')::TEXT = 'updated_at' AND sqlc.arg('order')::TEXT = 'ASC' THEN updated_at END ASC,
    CASE WHEN sqlc.arg('sort')::TEXT = 'updated_at' AND sqlc.arg('order')::TEXT = 'DESC' THEN updated_at END DESC
LIMIT sqlc.arg('limit') OFFSET sqlc.arg('offset');