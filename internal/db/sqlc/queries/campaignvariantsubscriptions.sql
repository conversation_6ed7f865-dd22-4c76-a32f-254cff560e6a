-- name: AddCampaignVariantSubscription :one
INSERT INTO campaign_variant_subscriptions (user_id, campaign_variant_id, vtuber_id, price,campaign_id, comment)
VALUES ($1,$2,$3,$4,$5, $6) RETURNING *;

-- name: DeleteCampaignVariantSubscription :exec
DELETE FROM campaign_variant_subscriptions WHERE id=$1;

-- name: GetCampaignVariantSubscriptionByUserIdAndCampaignVariantId :one
SELECT * FROM campaign_variant_subscriptions WHERE user_id=$1 AND campaign_variant_id=$2;

-- name: GetCampaignVariantSubscriptionCount :one
SELECT COUNT(*) FROM campaign_variant_subscriptions WHERE campaign_variant_id=$1;

-- name: PopularCampaign :one
SELECT SUM(cvs.price) as price, cvs.campaign_id
FROM campaign_variant_subscriptions cvs 
INNER JOIN campaigns c ON c.id = cvs.campaign_id 
WHERE c.deleted_at IS NULL
GROUP BY cvs.campaign_id ORDER BY price DESC LIMIT 1;