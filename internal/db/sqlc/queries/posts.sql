-- name: AddPost :one
WITH inserted as (INSERT INTO posts (vtuber_profile_id, membership_only, description, title, name, media, media_type,
                                     category_id, short_description, slug, campaign_id) VALUES ($1, $2, $3, $4, $5, $6, $7, $8, $9, $10, $11)
    RETURNING *)
SELECT inserted.*, vp.id as vtuber_id, vp.display_name as vtuber_name, vp.image as vtuber_image
FROM inserted
         INNER JOIN vtuber_profiles vp ON inserted.vtuber_profile_id = vp.id;

-- name: GetPost :one
 SELECT * FROM posts WHERE (id::TEXT = sqlc.arg('id')::TEXT OR slug::TEXT = sqlc.arg('id')::TEXT);

-- name: GetPostByID :one
SELECT p.*, vp.id as vtuber_id, vp.display_name as vtuber_name, vp.image as vtuber_image,
COALESCE(likes.like_count, 0) as likes,
COALESCE(comments.post_comments_count, 0) as comments,
CASE
               WHEN sqlc.narg('user_id')::BIGINT IS NULL THEN FALSE
WHEN EXISTS (SELECT 1
          FROM post_likes pl
          WHERE pl.post_id = p.id
            AND pl.user_id = sqlc.narg('user_id')::BIGINT)
 THEN TRUE
ELSE FALSE
END as has_liked
FROM posts as p
         INNER JOIN vtuber_profiles as vp ON vp.id = p.vtuber_profile_id
         LEFT JOIN (SELECT COUNT(id) AS like_count, post_id
        FROM post_likes
        GROUP BY post_id) AS likes ON likes.post_id = p.id
        LEFT JOIN (SELECT COUNT(id) AS post_comments_count, post_id
                   FROM post_comments
                   GROUP BY post_id) AS comments ON comments.post_id = p.id
WHERE (p.id::TEXT = sqlc.arg('id')::TEXT OR p.slug::TEXT = sqlc.arg('id')::TEXT)
  AND p.deleted_at IS NULL;

-- name: ListPosts :many
WITH FILTERED AS (SELECT p.*, vp.id as vtuber_id, vp.display_name as vtuber_name, vp.image as vtuber_image,
COALESCE(likes.like_count, 0) as likes,
COALESCE(comments.post_comments_count, 0) as comments,
              CASE
                             WHEN sqlc.narg('user_id')::BIGINT IS NULL THEN FALSE
           WHEN EXISTS (SELECT 1
                        FROM post_likes pl
                        WHERE pl.post_id = p.id
                          AND pl.user_id = sqlc.narg('user_id')::BIGINT)
               THEN TRUE
           ELSE FALSE
           END as has_liked
                  FROM posts as p
                           INNER JOIN vtuber_profiles as vp ON vp.id = p.vtuber_profile_id
                     LEFT JOIN (SELECT COUNT(id) AS like_count, post_id
                    FROM post_likes
                    GROUP BY post_id) AS likes ON likes.post_id = p.id
         LEFT JOIN (SELECT COUNT(id) AS post_comments_count, post_id
                    FROM post_comments
                    GROUP BY post_id) AS comments ON comments.post_id = p.id
                  WHERE 
                  CASE 
                  WHEN sqlc.narg('campaign_id')::BIGINT IS NOT NULL THEN p.campaign_id = sqlc.narg('campaign_id')::BIGINT
                  WHEN sqlc.narg('vtuber_username')::TEXT IS NOT NULL THEN vp.username = sqlc.narg('vtuber_username')::TEXT AND campaign_id IS NULL
                  WHEN sqlc.narg('vtuber_id')::BIGINT IS NOT NULL THEN p.vtuber_profile_id = sqlc.narg('vtuber_id')::BIGINT 
                  ELSE TRUE
                  END
                    AND (p.category_id = COALESCE(sqlc.narg('category_id'), p.category_id))
                    AND p.deleted_at IS NULL),
     COUNTED AS (SELECT COUNT(*) AS total
                 FROM FILTERED)
SELECT f.*, c.total
FROM FILTERED f,
     COUNTED c
ORDER BY CASE WHEN sqlc.arg('sort')::TEXT = 'id' AND sqlc.arg('order')::TEXT = 'ASC' THEN id END ASC,
         CASE WHEN sqlc.arg('sort')::TEXT = 'id' AND sqlc.arg('order')::TEXT = 'DESC' THEN id END DESC,
         CASE WHEN sqlc.arg('sort')::TEXT = 'title' AND sqlc.arg('order')::TEXT = 'ASC' THEN title END ASC,
         CASE WHEN sqlc.arg('sort')::TEXT = 'title' AND sqlc.arg('order')::TEXT = 'DESC' THEN title END DESC,
         CASE WHEN sqlc.arg('sort')::TEXT = 'name' AND sqlc.arg('order')::TEXT = 'ASC' THEN name END ASC,
         CASE WHEN sqlc.arg('sort')::TEXT = 'name' AND sqlc.arg('order')::TEXT = 'DESC' THEN name END DESC,
         CASE WHEN sqlc.arg('sort')::TEXT = 'category_id' AND sqlc.arg('order')::TEXT = 'ASC' THEN category_id END ASC,
         CASE
             WHEN sqlc.arg('sort')::TEXT = 'category_id' AND sqlc.arg('order')::TEXT = 'DESC' THEN category_id END DESC,
         CASE WHEN sqlc.arg('sort')::TEXT = 'created_at' AND sqlc.arg('order')::TEXT = 'ASC' THEN created_at END ASC,
         CASE WHEN sqlc.arg('sort')::TEXT = 'created_at' AND sqlc.arg('order')::TEXT = 'DESC' THEN created_at END DESC,
         CASE WHEN sqlc.arg('sort')::TEXT = 'updated_at' AND sqlc.arg('order')::TEXT = 'ASC' THEN updated_at END ASC,
         CASE WHEN sqlc.arg('sort')::TEXT = 'updated_at' AND sqlc.arg('order')::TEXT = 'DESC' THEN updated_at END DESC
LIMIT sqlc.arg('limit') OFFSET sqlc.arg('offset');

-- name: DeletePost :exec
UPDATE posts
SET deleted_at = CURRENT_TIMESTAMP
WHERE id = $1;

-- name: UpdatePost :exec
UPDATE posts
SET title          = $1,
    description    = $2,
    media=$3,
    media_type=$4,
    name=$5,
    membership_only=$6,
    category_id=$7,
    short_description = $8,
    campaign_id = $9
WHERE id = $10;

-- name: GetVtuberGalleries :many
WITH FILTERED AS (SELECT * FROM posts WHERE deleted_at IS NULL AND media IS NOT NULL AND media_type IS NOT NULL AND vtuber_profile_id = $1),
     COUNTED AS (SELECT COUNT(*) AS total FROM FILTERED)
SELECT f.*, c.total
FROM FILTERED f,
     COUNTED c
ORDER BY CASE WHEN sqlc.arg('sort')::TEXT = 'created_at' AND sqlc.arg('order')::TEXT = 'ASC' THEN created_at END ASC,
         CASE WHEN sqlc.arg('sort')::TEXT = 'created_at' AND sqlc.arg('order')::TEXT = 'DESC' THEN created_at END DESC
LIMIT sqlc.arg('limit') OFFSET sqlc.arg('offset');

-- name: GetPostBySlug :one
SELECT  * FROM posts WHERE slug = $1;