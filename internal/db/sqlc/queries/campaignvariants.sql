-- name: AddCampaignVariant :one
INSERT INTO campaign_variants (description, price, max_sub, image, campaign_id, title)
VALUES ($1, $2, $3, $4, $5, $6) RETURNING *;

-- name: InsertManyCampaignVariants :copyfrom
INSERT INTO campaign_variants (description, price, max_sub, image, campaign_id, title) VALUES ($1, $2, $3, $4, $5, $6);

-- name: GetCampaignVariantById :one
SELECT 
    cv.id, 
    cv.campaign_id, 
    cv.price, 
    cv.title,
    cv.max_sub,
    cv.description, 
    cv.image, 
    cv.created_at, 
    COALESCE(t.count , 0) AS sub_count
FROM campaign_variants cv
LEFT JOIN (
    SELECT 
        campaign_variant_id, 
        COUNT(id) AS count
    FROM campaign_variant_subscriptions
    GROUP BY campaign_variant_id
) t ON cv.id = t.campaign_variant_id
WHERE cv.id = $1 AND cv.deleted_at IS NULL;

-- name: GetAllCampaignVariantsByCampaignId :many
SELECT 
  cv.id,
  cv.campaign_id,
  cv.price,
  cv.title,
  cv.max_sub,
  cv.description,
  cv.image,
  cv.created_at,
  COALESCE(t.count, 0) AS sub_count,
  CASE
    WHEN sqlc.narg('user_id')::BIGINT  IS NULL THEN FALSE
    WHEN EXISTS (
      SELECT 1
      FROM campaign_variant_subscriptions t2
      WHERE t2.campaign_variant_id = cv.id
        AND t2.user_id = sqlc.narg('user_id')::BIGINT
    ) THEN TRUE
    ELSE FALSE
  END AS has_subscribed

FROM campaign_variants cv
LEFT JOIN (
  SELECT campaign_variant_id, COUNT(id) AS count
  FROM campaign_variant_subscriptions
  GROUP BY campaign_variant_id
) t ON cv.id = t.campaign_variant_id
WHERE cv.campaign_id = sqlc.arg('campaign_id')::BIGINT AND cv.deleted_at IS NULL 
ORDER BY created_at ASC;
   

-- name: UpdateCampaignVariantById :exec
UPDATE campaign_variants SET description = $1, price = $2, max_sub = $3, image = $4, title = $5 WHERE id = $6;

-- name: DeleteCampaignVariantById :exec
UPDATE campaign_variants SET deleted_at = CURRENT_TIMESTAMP WHERE id = $1;

-- name: GetSubsOfVariant :many
WITH FILTERED AS (SELECT t.campaign_variant_id, t.price, u.full_name as user_name, u.id as userId, u.image as user_image, t.created_at FROM campaign_variant_subscriptions t INNER JOIN users u ON t.user_id=u.id WHERE campaign_variant_id = $1),
     COUNTED AS(
         SELECT COUNT(*) AS total FROM FILTERED
     )
SELECT f.*, c.total FROM FILTERED f, COUNTED c
ORDER BY
    created_at DESC
LIMIT sqlc.arg('limit') OFFSET sqlc.arg('offset');

-- name: GetOneCampaignVariantById :one
SELECT * from campaign_variants WHERE id=$1;

