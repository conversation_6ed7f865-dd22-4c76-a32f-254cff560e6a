package vtubers

import (
	"context"
	"errors"
	"log"
	"strconv"

	storagev1 "github.com/nsp-inc/vtuber/internal/storage"

	"github.com/nsp-inc/vtuber/packages/helpers"
	"github.com/nsp-inc/vtuber/packages/validation"
	"github.com/nsp-inc/vtuber/packages/web"

	"connectrpc.com/connect"
	sharedv1 "github.com/nsp-inc/vtuber/api/shared/v1"
	vtubersv1 "github.com/nsp-inc/vtuber/api/vtubers/v1"
	categoriesqueries "github.com/nsp-inc/vtuber/internal/db/sqlc/tables/categories"
	favoritevtubersqueries "github.com/nsp-inc/vtuber/internal/db/sqlc/tables/favoritevtubers"
	usersv1repo "github.com/nsp-inc/vtuber/internal/db/sqlc/tables/users"
	vtubercategoriesqueries "github.com/nsp-inc/vtuber/internal/db/sqlc/tables/vtubercategories"
	vtuberprofilesqueries "github.com/nsp-inc/vtuber/internal/db/sqlc/tables/vtuberprofiles"
	vtuberrequestsqueries "github.com/nsp-inc/vtuber/internal/db/sqlc/tables/vtuberrequests"
	vtuberusersubscriptionsqueries "github.com/nsp-inc/vtuber/internal/db/sqlc/tables/vtuberusersubscriptions"
	"google.golang.org/protobuf/types/known/timestamppb"
)

type VtuberService struct {
	favoriteVtuberRepo   *favoritevtubersqueries.Queries
	repo                 *vtuberprofilesqueries.Queries
	vtuberrequestsRepo   *vtuberrequestsqueries.Queries
	userRepo             *usersv1repo.Queries
	storageService       *storagev1.StorageService
	creatorUserSubRepo   *vtuberusersubscriptionsqueries.Queries
	categoryRepo         *categoriesqueries.Queries
	vtuberCategoriesRepo *vtubercategoriesqueries.Queries
}

func NewVtuberService(userRepo *usersv1repo.Queries, vtuberRepo *vtuberprofilesqueries.Queries, storageService *storagev1.StorageService, creatorUserSubRepo *vtuberusersubscriptionsqueries.Queries, favoriteVtuberRepo *favoritevtubersqueries.Queries, vtuberrequestsrepo *vtuberrequestsqueries.Queries, categoryRepo *categoriesqueries.Queries, vtuberCategoriesRepo *vtubercategoriesqueries.Queries) *VtuberService {

	return &VtuberService{
		repo:                 vtuberRepo,
		userRepo:             userRepo,
		storageService:       storageService,
		creatorUserSubRepo:   creatorUserSubRepo,
		favoriteVtuberRepo:   favoriteVtuberRepo,
		vtuberrequestsRepo:   vtuberrequestsrepo,
		categoryRepo:         categoryRepo,
		vtuberCategoriesRepo: vtuberCategoriesRepo,
	}
}

func (v VtuberService) AddVtuberProfile(ctx context.Context, c *connect.Request[vtubersv1.AddVtuberProfileRequest]) (*connect.Response[vtubersv1.AddVtuberProfileResponse], error) {
	userId := web.GetUserFromContext(ctx).ID
	_, err := v.repo.GetVtuberProfileByUserID(ctx, userId)
	if err == nil {
		return nil, errors.New(
			web.GetTranslation(ctx, "creatorProfileAlreadyExist", nil),
		)
	}

	_, err = v.repo.GetVtuberProfileByUsername(ctx, c.Msg.Username)
	if err == nil {
		return nil, errors.New(
			web.GetTranslation(ctx, "usernameAlreadyTaken", nil),
		)
	}

	if len(c.Msg.Categories) > 0 {
		categories, err := v.categoryRepo.GetCategoriesByIds(ctx, c.Msg.Categories)
		if err != nil {

			return nil, err
		}
		if len(categories) != len(c.Msg.Categories) {
			return nil, errors.New(web.GetTranslation(ctx, "fieldNotFound", map[string]string{
				"field": web.GetTranslation(ctx, "category", nil),
			}))
		}
	}

	newImageName, err := v.storageService.ValidateAndUploadFromTemp(ctx, &c.Msg.Image, storagev1.ImageFileType, "vtuber-image", true, nil)
	if err != nil {
		return nil, validation.NewFieldError("image", err)
	}

	newBannerImageName, err := v.storageService.ValidateAndUploadFromTemp(ctx, &c.Msg.BannerImage, storagev1.ImageFileType, "vtuber-banner", true, nil)
	if err != nil {
		return nil, validation.NewFieldError("banner_image", err)
	}

	tx, err := web.StartTransaction(ctx)
	if err != nil {
		return nil, err
	}
	defer tx.Rollback(ctx)
	vtuberProfileRepo := v.repo.WithTx(tx)
	catRepo := v.vtuberCategoriesRepo.WithTx(tx)

	profile, err := vtuberProfileRepo.CreateVtuberProfile(
		ctx,
		vtuberprofilesqueries.CreateVtuberProfileParams{
			UserID:           userId,
			DisplayName:      c.Msg.DisplayName,
			Furigana:         c.Msg.Furigana,
			Image:            helpers.GetPointerString(newImageName),
			BannerImage:      helpers.GetPointerString(newBannerImageName),
			SocialMediaLinks: c.Msg.SocialMediaLinks.ToBytes(),
			Username:         c.Msg.Username,
		},
	)
	if err != nil {
		return nil, err
	}

	_, err = catRepo.AddVtuberCategories(ctx, helpers.Map(c.Msg.Categories, func(categoryId int64) vtubercategoriesqueries.AddVtuberCategoriesParams {
		return vtubercategoriesqueries.AddVtuberCategoriesParams{
			VtuberProfileID: profile.ID,
			CategoryID:      categoryId,
		}
	}))
	if err != nil {
		return nil, err
	}
	err = tx.Commit(ctx)
	if err != nil {
		return nil, err
	}

	return connect.NewResponse(&vtubersv1.AddVtuberProfileResponse{
		Data: &vtubersv1.VtuberProfile{
			Id:               profile.ID,
			UserId:           profile.UserID,
			DisplayName:      profile.DisplayName,
			Furigana:         profile.Furigana,
			Image:            helpers.GetCdnLinkPointer(&newImageName),
			BannerImage:      helpers.GetCdnLinkPointer(&newBannerImageName),
			SocialMediaLinks: sharedv1.SocialMediaLinksFromBytes(profile.SocialMediaLinks),
			Description:      profile.Description,
			CreatedAt:        timestamppb.New(profile.CreatedAt),
			Username:         profile.Username,
			Categories:       c.Msg.Categories,
		},
	}), nil

}

func (v VtuberService) GetVtuberProfileById(ctx context.Context, c *connect.Request[vtubersv1.GetVtuberProfileByIdRequest]) (*connect.Response[vtubersv1.GetVtuberProfileByIdResponse], error) {

	profile, err := v.repo.GetVtuberProfileByID(ctx, c.Msg.Id)
	if err != nil {
		return nil, errors.New(
			web.GetTranslation(ctx, "fieldNotFound", map[string]string{
				"field": web.GetTranslation(ctx, "creatorProfile", nil),
			}),
		)
	}
	user := web.GetUserFromContext(ctx)
	isSubscribed := false
	if user != nil {
		count, err := v.creatorUserSubRepo.IsUserSubscribed(ctx, vtuberusersubscriptionsqueries.IsUserSubscribedParams{
			VtuberID: strconv.FormatInt(profile.ID, 10),
			UserID:   user.ID,
		})

		if err != nil {
			return nil, err
		}
		if count > 0 {
			isSubscribed = true
		}
	}

	hasLiked := false
	if user != nil {
		_, err := v.favoriteVtuberRepo.GetFavoriteVtuberByUserIdAndVtuberId(ctx, favoritevtubersqueries.GetFavoriteVtuberByUserIdAndVtuberIdParams{
			UserID:   user.ID,
			VtuberID: profile.ID,
		})
		if err == nil {
			hasLiked = true
		}
	}
	return connect.NewResponse(&vtubersv1.GetVtuberProfileByIdResponse{
		Data: &vtubersv1.VtuberProfile{
			Id:               profile.ID,
			UserId:           profile.UserID,
			DisplayName:      profile.DisplayName,
			Furigana:         profile.Furigana,
			Image:            helpers.GetCdnLinkPointer(profile.Image),
			BannerImage:      helpers.GetCdnLinkPointer(profile.BannerImage),
			Description:      profile.Description,
			SocialMediaLinks: sharedv1.SocialMediaLinksFromBytes(profile.SocialMediaLinks),
			CreatedAt:        timestamppb.New(profile.CreatedAt),
			IsUserSubscribed: isSubscribed,
			HasLiked:         hasLiked,
			Username:         profile.Username,
			Categories:       profile.CategoryIds,
		},
	}), nil
}

func (v VtuberService) GetVtuberProfile(ctx context.Context, _ *connect.Request[vtubersv1.GetVtuberProfileRequest]) (*connect.Response[vtubersv1.GetVtuberProfileByIdResponse], error) {
	sessionUser := web.GetUserFromContext(ctx)
	profile, err := v.repo.GetVtuberProfileByUserID(ctx, sessionUser.ID)
	if err != nil {
		return nil, errors.New(
			web.GetTranslation(ctx, "fieldNotFound", map[string]string{
				"field": web.GetTranslation(ctx, "creatorProfile", nil),
			}),
		)
	}
	return connect.NewResponse(&vtubersv1.GetVtuberProfileByIdResponse{
		Data: &vtubersv1.VtuberProfile{
			Id:               profile.ID,
			UserId:           profile.UserID,
			DisplayName:      profile.DisplayName,
			Furigana:         profile.Furigana,
			Image:            helpers.GetCdnLinkPointer(profile.Image),
			BannerImage:      helpers.GetCdnLinkPointer(profile.BannerImage),
			Description:      profile.Description,
			CreatedAt:        timestamppb.New(profile.CreatedAt),
			SocialMediaLinks: sharedv1.SocialMediaLinksFromBytes(profile.SocialMediaLinks),
			Username:         profile.Username,
			Categories:       profile.CategoryIds,
		},
	}), nil
}

func (v VtuberService) UpdateVtuberProfile(ctx context.Context, c *connect.Request[vtubersv1.UpdateVtuberProfileRequest]) (*connect.Response[vtubersv1.UpdateVtuberProfileResponse], error) {
	sessionUser := web.GetUserFromContext(ctx)
	profile, err := v.repo.GetVtuberProfileByID(ctx, strconv.FormatInt(c.Msg.Id, 10))
	log.Println(err, "ERROR")
	if err != nil {
		return nil, errors.New(
			web.GetTranslation(ctx, "fieldNotFound", map[string]string{
				"field": web.GetTranslation(ctx, "creatorProfile", nil),
			}),
		)
	}

	if profile.UserID != sessionUser.ID {
		return nil, errors.New(
			web.GetTranslation(ctx, "unauthorizedToPerformAction", nil),
		)
	}

	if len(c.Msg.Categories) > 0 {
		categories, err := v.categoryRepo.GetCategoriesByIds(ctx, c.Msg.Categories)
		if err != nil {

			return nil, err
		}
		if len(categories) != len(c.Msg.Categories) {
			return nil, errors.New(web.GetTranslation(ctx, "fieldNotFound", map[string]string{
				"field": web.GetTranslation(ctx, "category", nil),
			}))
		}
	}

	newVtuberImage, err := v.storageService.ValidateAndUploadFromTemp(ctx, &c.Msg.Image, storagev1.ImageFileType, "vtuber-image", false, profile.Image)
	if err != nil {
		return nil, validation.NewFieldError("image", err)
	}

	newBannerImage, err := v.storageService.ValidateAndUploadFromTemp(ctx, &c.Msg.BannerImage, storagev1.ImageFileType, "vtuber-banner", false, profile.BannerImage)
	if err != nil {
		return nil, validation.NewFieldError("banner_image", err)
	}

	tx, err := web.StartTransaction(ctx)
	if err != nil {
		return nil, err
	}
	defer tx.Rollback(ctx)
	vtuberProfileRepo := v.repo.WithTx(tx)
	catRepo := v.vtuberCategoriesRepo.WithTx(tx)

	_, err = vtuberProfileRepo.UpdateVtuberProfile(
		ctx,
		vtuberprofilesqueries.UpdateVtuberProfileParams{
			ID:               profile.ID,
			DisplayName:      c.Msg.DisplayName,
			Furigana:         c.Msg.Furigana,
			Image:            helpers.GetPointerString(newVtuberImage),
			BannerImage:      helpers.GetPointerString(newBannerImage),
			SocialMediaLinks: c.Msg.GetSocialMediaLinks().ToBytes(),
			Description:      c.Msg.Description,
		},
	)
	if err != nil {
		return nil, err
	}

	err = catRepo.DeleteVtuberCategories(ctx, profile.ID)
	if err != nil {
		return nil, err
	}

	_, err = catRepo.AddVtuberCategories(ctx, helpers.Map(c.Msg.Categories, func(categoryId int64) vtubercategoriesqueries.AddVtuberCategoriesParams {
		return vtubercategoriesqueries.AddVtuberCategoriesParams{
			VtuberProfileID: profile.ID,
			CategoryID:      categoryId,
		}
	}))
	if err != nil {
		return nil, err
	}

	err = tx.Commit(ctx)
	if err != nil {
		return nil, err
	}

	return connect.NewResponse(&vtubersv1.UpdateVtuberProfileResponse{
		Message: web.GetTranslation(ctx, "creatorProfileUpdated", nil),
		Success: true,
	}), nil

}

func (v VtuberService) GetAllVtuberProfiles(ctx context.Context, c *connect.Request[vtubersv1.GetAllVtuberProfilesRequest]) (*connect.Response[vtubersv1.GetAllVtuberProfilesResponse], error) {
	paginationInfo := sharedv1.GetPaginationRequestInfo(c.Msg.Pagination, []string{"display_name", "created_at", "updated_at"})
	profiles, err := v.repo.ListVtuberProfiles(ctx, vtuberprofilesqueries.ListVtuberProfilesParams{
		Limit:       paginationInfo.PageSize,
		Offset:      paginationInfo.Offset,
		Order:       paginationInfo.OrderDirection,
		Sort:        paginationInfo.OrderBy,
		DisplayName: c.Msg.DisplayName,
		CategoryID:  c.Msg.CategoryId,
	})
	if err != nil {
		return nil, err
	}
	return connect.NewResponse(&vtubersv1.GetAllVtuberProfilesResponse{
		Data: helpers.Map(profiles, func(profile vtuberprofilesqueries.ListVtuberProfilesRow) *vtubersv1.VtuberProfile {
			return &vtubersv1.VtuberProfile{
				Id:               profile.ID,
				UserId:           profile.UserID,
				DisplayName:      profile.DisplayName,
				Furigana:         profile.Furigana,
				Image:            helpers.GetCdnLinkPointer(profile.Image),
				BannerImage:      helpers.GetCdnLinkPointer(profile.BannerImage),
				Description:      profile.Description,
				CreatedAt:        timestamppb.New(profile.CreatedAt),
				SocialMediaLinks: sharedv1.SocialMediaLinksFromBytes(profile.SocialMediaLinks),
				Username:         profile.Username,
				Categories:       profile.CategoryIds,
			}
		}),
		PaginationDetails: sharedv1.GetPaginationResponseInfo(paginationInfo, helpers.GetFirstElement(profiles)),
	}), nil
}

func (v VtuberService) DeleteVtuberProfileById(ctx context.Context, c *connect.Request[vtubersv1.DeleteVtuberProfileByIdRequest]) (*connect.Response[sharedv1.GenericResponse], error) {
	sessionUser := web.GetUserFromContext(ctx)
	profile, err := v.repo.GetVtuberProfileByID(ctx, strconv.FormatInt(c.Msg.Id, 10))
	if err != nil {
		return nil, errors.New(
			web.GetTranslation(ctx, "fieldNotFound", map[string]string{
				"field": web.GetTranslation(ctx, "creatorProfile", nil),
			}),
		)
	}
	if profile.UserID != sessionUser.ID {
		return nil, errors.New(
			web.GetTranslation(ctx, "unauthorizedToPerformAction", nil),
		)
	}

	err = v.repo.DeleteVtuberProfileByID(ctx, profile.ID)
	if err != nil {
		return nil, err
	}
	return connect.NewResponse(&sharedv1.GenericResponse{
		Status:  200,
		Message: web.GetTranslation(ctx, "creatorProfileDeleted", nil),
		Success: true,
	}), nil
}

func (v VtuberService) VerifyVtuberProfile(ctx context.Context, c *connect.Request[vtubersv1.VerifyVtuberProfileRequest]) (*connect.Response[vtubersv1.VerifyVtuberProfileResponse], error) {
	creatorReq, err := v.vtuberrequestsRepo.GetCreatorRequestByID(ctx, c.Msg.Id)
	if err != nil {
		return nil, errors.New(
			web.GetTranslation(ctx, "notAppliedForCreator", nil),
		)
	}
	tx, err := web.StartTransaction(ctx)
	if err != nil {
		return nil, err
	}
	defer tx.Rollback(ctx)
	qtx := v.vtuberrequestsRepo.WithTx(tx)
	profileqtx := v.repo.WithTx(tx)

	err = qtx.ApproveCreatorRequest(ctx, creatorReq.ID)
	if err != nil {
		return nil, err
	}
	err = profileqtx.VerifyVtuberProfile(ctx, creatorReq.UserID)
	if err != nil {
		return nil, err
	}
	err = tx.Commit(ctx)
	if err != nil {
		return nil, err
	}
	return connect.NewResponse(&vtubersv1.VerifyVtuberProfileResponse{
		Message: web.GetTranslation(ctx, "creatorProfileVerified", nil),
		Success: true,
	}), nil
}

func (v VtuberService) ApplyVtuberAccess(ctx context.Context, c *connect.Request[vtubersv1.CreateVtuberProfileAccessRequest]) (*connect.Response[vtubersv1.CreateVtuberProfileAccessResponse], error) {
	sessionUser := web.GetUserFromContext(ctx)
	if sessionUser.IsVtuber {
		return nil, errors.New(
			web.GetTranslation(ctx, "userAlreadyCreator", nil),
		)
	}

	if sessionUser.IsAdmin() {
		return nil, errors.New(
			web.GetTranslation(ctx, "adminCannotApplyForCreator", nil))
	}
	_, err := v.vtuberrequestsRepo.GetCreatorRequestByUserID(ctx, sessionUser.ID)

	if err == nil {
		return nil, errors.New(
			web.GetTranslation(ctx, "userAlreadyAppliedForCreator", nil),
		)
	}
	err = v.vtuberrequestsRepo.ApplyForVtuberAccess(ctx,
		vtuberrequestsqueries.ApplyForVtuberAccessParams{
			UserID:      sessionUser.ID,
			Description: c.Msg.Description,
		})
	if err != nil {
		return nil, err
	}

	return connect.NewResponse(&vtubersv1.CreateVtuberProfileAccessResponse{

		Message: web.GetTranslation(ctx, "appliedForCreator", nil),
		Success: true,
	}), nil
}

func (v VtuberService) GetAllVtuberProfileAccess(ctx context.Context, c *connect.Request[vtubersv1.GetAllVtuberProfileAccessRequest]) (*connect.Response[vtubersv1.GetAllVtuberProfileAccessResponse], error) {
	paginationInfo := sharedv1.GetPaginationRequestInfo(c.Msg.Pagination, []string{"status", "created_at", "updated_at"})
	accesses, err := v.vtuberrequestsRepo.GetAllCreatorRequests(ctx, vtuberrequestsqueries.GetAllCreatorRequestsParams{
		Limit:  paginationInfo.PageSize,
		Offset: paginationInfo.Offset,
		Order:  paginationInfo.OrderDirection,
		Sort:   paginationInfo.OrderBy,
	})
	if err != nil {
		return nil, err
	}
	return connect.NewResponse(&vtubersv1.GetAllVtuberProfileAccessResponse{
		Data: helpers.Map(accesses, func(access vtuberrequestsqueries.GetAllCreatorRequestsRow) *vtubersv1.VtuberAccessRequest {
			return &vtubersv1.VtuberAccessRequest{
				Id:          access.ID,
				Description: access.Description,
				Status:      string(access.Status),
				Reason:      access.Reason,
				UserId:      access.UserID,
				Name:        access.Name,
				CreatedAt:   timestamppb.New(access.CreatedAt),
			}
		}),
		PaginationDetails: sharedv1.GetPaginationResponseInfo(paginationInfo, helpers.GetFirstElement(accesses)),
	}), nil

}

func (v VtuberService) DenyVtuberProfile(ctx context.Context, c *connect.Request[vtubersv1.DenyVtuberProfileRequest]) (*connect.Response[vtubersv1.DenyVtuberProfileResponse], error) {
	_, err := v.vtuberrequestsRepo.GetCreatorRequestByID(ctx, c.Msg.Id)
	if err != nil {
		return nil, errors.New(
			web.GetTranslation(ctx, "fieldNotFound", map[string]string{
				"field": web.GetTranslation(ctx, "creatorAccessRequest", nil),
			}),
		)
	}

	err = v.vtuberrequestsRepo.DenyCreatorRequest(ctx, vtuberrequestsqueries.DenyCreatorRequestParams{
		Reason: &c.Msg.Reason,
		ID:     c.Msg.Id,
	})
	if err != nil {
		return nil, err
	}

	return connect.NewResponse(&vtubersv1.DenyVtuberProfileResponse{
		Message: web.GetTranslation(ctx, "creatorAccessDenied", nil),
		Success: true,
	}), nil
}

func (v *VtuberService) GetMyVtuberAccessRequests(ctx context.Context, _ *connect.Request[vtubersv1.GetAllVtuberProfileAccessRequest]) (*connect.Response[vtubersv1.VtuberAccessRequest], error) {
	sessionUser := web.GetUserFromContext(ctx)
	access, err := v.vtuberrequestsRepo.GetCreatorRequestByUserID(ctx, sessionUser.ID)
	if err != nil {
		return nil, errors.New(
			web.GetTranslation(ctx, "fieldNotFound", map[string]string{
				"field": web.GetTranslation(ctx, "creatorAccessRequest", nil),
			}),
		)
	}
	return connect.NewResponse(&vtubersv1.VtuberAccessRequest{
		Id:          access.ID,
		Description: access.Description,
		Status:      string(access.Status),
		Reason:      access.Reason,
		UserId:      access.UserID,
		CreatedAt:   timestamppb.New(access.CreatedAt),
	}), nil
}

func (v *VtuberService) UpdateVtuberAccessRequest(ctx context.Context, c *connect.Request[vtubersv1.UpdateVtuberProfileAccessRequest]) (*connect.Response[vtubersv1.UpdateVtuberProfileAccessResponse], error) {
	previousRequest, err := v.vtuberrequestsRepo.GetCreatorRequestByUserID(ctx, web.GetUserFromContext(ctx).ID)
	if err != nil {
		return nil, errors.New(
			web.GetTranslation(ctx, "fieldNotFound", map[string]string{
				"field": web.GetTranslation(ctx, "creatorAccessRequest", nil),
			}),
		)
	}

	if previousRequest.Status == vtuberrequestsqueries.VtuberAccessStatusApproved {
		return nil, errors.New(
			web.GetTranslation(ctx, "creatorAccessAlreadyApproved", nil),
		)
	}

	err = v.vtuberrequestsRepo.UpdateCreatorRequest(ctx, vtuberrequestsqueries.UpdateCreatorRequestParams{
		ID:          previousRequest.ID,
		Description: c.Msg.Description,
	})

	if err != nil {
		return nil, err
	}

	return connect.NewResponse(&vtubersv1.UpdateVtuberProfileAccessResponse{
		Message: web.GetTranslation(ctx, "creatorAccessRequestUpdated", nil),
		Success: true,
	}), nil
}
