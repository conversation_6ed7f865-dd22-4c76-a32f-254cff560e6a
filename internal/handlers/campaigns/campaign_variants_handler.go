package campaigns

import (
	"context"
	"errors"
	"strconv"

	campaignsv1 "github.com/nsp-inc/vtuber/api/campaigns/v1"
	sharedv1 "github.com/nsp-inc/vtuber/api/shared/v1"
	campaignsqueries "github.com/nsp-inc/vtuber/internal/db/sqlc/tables/campaigns"
	campaignvariantsqueries "github.com/nsp-inc/vtuber/internal/db/sqlc/tables/campaignvariants"
	transactionsqueries "github.com/nsp-inc/vtuber/internal/db/sqlc/tables/transactions"
	storagev1 "github.com/nsp-inc/vtuber/internal/storage"
	"github.com/nsp-inc/vtuber/packages/validation"

	"connectrpc.com/connect"
	"github.com/nsp-inc/vtuber/packages/helpers"
	"github.com/nsp-inc/vtuber/packages/web"
	"google.golang.org/protobuf/types/known/timestamppb"
)

type CampaignVariantService struct {
	repo            *campaignvariantsqueries.Queries
	campaignRepo    *campaignsqueries.Queries
	transactionRepo *transactionsqueries.Queries
	storageService  *storagev1.StorageService
}

func (c CampaignVariantService) GetCampaignVariantSubs(ctx context.Context, req *connect.Request[campaignsv1.GetCampaignSubRequest]) (*connect.Response[campaignsv1.GetCampaignSubResponse], error) {
	paginationInfo := sharedv1.GetPaginationRequestInfo(req.Msg.Pagination, []string{"created_at"})
	variant, err := c.repo.GetOneCampaignVariantById(ctx, req.Msg.Id)
	if err != nil {
		return nil, errors.New(
			web.GetTranslation(ctx, "fieldNotFound", map[string]string{
				"field": web.GetTranslation(ctx, "variant", nil),
			}),
		)
	}

	subs, err := c.repo.GetSubsOfVariant(ctx, campaignvariantsqueries.GetSubsOfVariantParams{
		CampaignVariantID: variant.ID,
		Limit:             paginationInfo.PageSize,
		Offset:            paginationInfo.Offset,
	})
	if err != nil {
		return nil, err
	}
	return connect.NewResponse(&campaignsv1.GetCampaignSubResponse{
		Subs: helpers.Map(subs, func(sub campaignvariantsqueries.GetSubsOfVariantRow) *campaignsv1.SubDetails {
			return &campaignsv1.SubDetails{
				Id:     sub.Userid,
				Name:   sub.UserName,
				Amount: sub.Price,
				Image:  helpers.GetCdnLinkPointer(sub.UserImage),
			}
		}),
		Pagination: sharedv1.GetPaginationResponseInfo(paginationInfo, helpers.GetFirstElement(subs)),
	}), nil
}

func NewCampaignVariantService(
	repo *campaignvariantsqueries.Queries,
	campaignRepo *campaignsqueries.Queries,
	transactionRepo *transactionsqueries.Queries,
	storageService *storagev1.StorageService,
) *CampaignVariantService {
	return &CampaignVariantService{
		repo:            repo,
		campaignRepo:    campaignRepo,
		transactionRepo: transactionRepo,
		storageService:  storageService,
	}
}

func (c CampaignVariantService) AddCampaignVariant(ctx context.Context, c2 *connect.Request[campaignsv1.AddCampaignVariantRequest]) (*connect.Response[campaignsv1.AddCampaignVariantResponse], error) {
	sessionUser := web.GetUserFromContext(ctx)
	camp, err := c.campaignRepo.GetCampaignById(ctx, strconv.FormatInt(c2.Msg.CampaignId, 10))
	if err != nil {
		return nil, errors.New(
			web.GetTranslation(ctx, "fieldNotFound", map[string]string{
				"field": web.GetTranslation(ctx, "campaign", nil),
			}),
		)
	}
	if *sessionUser.VtuberId != camp.VtuberID {
		return nil, errors.New(
			web.GetTranslation(ctx, "unauthorizedVariantAdd", nil),
		)

	}

	newImage, err := c.storageService.ValidateAndUploadFromTemp(ctx, &c2.Msg.Image, storagev1.ImageFileType, "campaign_variant", true, nil)
	if err != nil {
		return nil, validation.NewFieldError("image", err)
	}

	variant, err := c.repo.AddCampaignVariant(ctx, campaignvariantsqueries.AddCampaignVariantParams{
		CampaignID:  c2.Msg.CampaignId,
		Image:       newImage,
		Description: c2.Msg.Description,
		MaxSub:      c2.Msg.MaxSub,
		Price:       c2.Msg.Price,
		Title:       c2.Msg.Title,
	})
	if err != nil {
		return nil, err
	}

	return connect.NewResponse(&campaignsv1.AddCampaignVariantResponse{
		Data: &campaignsv1.CampaignVariant{
			Id:          variant.ID,
			CampaignId:  variant.CampaignID,
			Description: variant.Description,
			Image:       helpers.GetCdnUrl(variant.Image),
			MaxSub:      variant.MaxSub,
			Title:       variant.Title,
			Price:       variant.Price,
			CreatedAt:   timestamppb.New(variant.CreatedAt),
		},
	}), nil
}

func (c CampaignVariantService) GetAllCampaignVariants(ctx context.Context, c2 *connect.Request[campaignsv1.GetAllCampaignVariantsRequest]) (*connect.Response[campaignsv1.GetAllCampaignVariantsResponse], error) {
	variants, err := c.repo.GetAllCampaignVariantsByCampaignId(ctx, campaignvariantsqueries.GetAllCampaignVariantsByCampaignIdParams{
		CampaignID: c2.Msg.CampaignId,
	})
	if err != nil {
		return nil, err
	}
	return connect.NewResponse(&campaignsv1.GetAllCampaignVariantsResponse{
		Data: helpers.Map(variants, func(variant campaignvariantsqueries.GetAllCampaignVariantsByCampaignIdRow) *campaignsv1.CampaignVariant {
			return &campaignsv1.CampaignVariant{
				Id:          variant.ID,
				CampaignId:  variant.CampaignID,
				Description: variant.Description,
				Image:       helpers.GetCdnUrl(variant.Image),
				MaxSub:      variant.MaxSub,
				Price:       variant.Price,
				SubCount:    variant.SubCount,
				Title:       variant.Title,
				CreatedAt:   timestamppb.New(variant.CreatedAt),
			}
		}),
	}), nil
}

func (c CampaignVariantService) GetCampaignVariantById(ctx context.Context, c2 *connect.Request[campaignsv1.GetCampaignVariantByIdRequest]) (*connect.Response[campaignsv1.GetCampaignVariantByIdResponse], error) {
	variant, err := c.repo.GetCampaignVariantById(ctx, c2.Msg.Id)
	if err != nil {
		return nil, errors.New(
			web.GetTranslation(ctx, "fieldNotFound", map[string]string{
				"field": web.GetTranslation(ctx, "variant", nil),
			}),
		)
	}
	return connect.NewResponse(&campaignsv1.GetCampaignVariantByIdResponse{
		Data: &campaignsv1.CampaignVariant{
			Id:          variant.ID,
			CampaignId:  variant.CampaignID,
			Description: variant.Description,
			Image:       helpers.GetCdnUrl(variant.Image),
			MaxSub:      variant.MaxSub,
			Price:       variant.Price,
			Title:       variant.Title,
			SubCount:    variant.SubCount,
			CreatedAt:   timestamppb.New(variant.CreatedAt),
		},
	}), nil
}

func (c CampaignVariantService) DeleteCampaignVariantById(ctx context.Context, c2 *connect.Request[campaignsv1.DeleteCampaignVariantByIdRequest]) (*connect.Response[campaignsv1.DeleteCampaignVariantByIdResponse], error) {
	sessionUser := web.GetUserFromContext(ctx)
	variant, err := c.repo.GetCampaignVariantById(ctx, c2.Msg.Id)
	if err != nil {
		return nil, errors.New(
			web.GetTranslation(ctx, "fieldNotFound", map[string]string{
				"field": web.GetTranslation(ctx, "variant", nil),
			}),
		)
	}
	camp, err := c.campaignRepo.GetCampaignById(ctx, strconv.FormatInt(variant.CampaignID, 10))
	if err != nil {
		return nil, errors.New(
			web.GetTranslation(ctx, "fieldNotFound", map[string]string{
				"field": web.GetTranslation(ctx, "campaign", nil),
			}),
		)
	}
	if *sessionUser.VtuberId != camp.VtuberID {
		return nil, errors.New(
			web.GetTranslation(ctx, "unauthorizedVariantDelete", nil),
		)
	}
	err = c.repo.DeleteCampaignVariantById(ctx, c2.Msg.Id)
	if err != nil {
		return nil, err
	}
	return connect.NewResponse(&campaignsv1.DeleteCampaignVariantByIdResponse{
		Success: true,
		Message: web.GetTranslation(ctx, "variantDeleted", nil),
	},
	), nil
}

func (c CampaignVariantService) UpdateCampaignVariantById(ctx context.Context, c2 *connect.Request[campaignsv1.UpdateCampaignVariantByIdRequest]) (*connect.Response[campaignsv1.UpdateCampaignVariantByIdResponse], error) {
	sessionUser := web.GetUserFromContext(ctx)
	variant, err := c.repo.GetCampaignVariantById(ctx, c2.Msg.Id)
	if err != nil {
		return nil, errors.New(
			web.GetTranslation(ctx, "fieldNotFound", map[string]string{
				"field": web.GetTranslation(ctx, "variant", nil),
			}),
		)
	}
	camp, err := c.campaignRepo.GetCampaignById(ctx, strconv.FormatInt(variant.CampaignID, 10))
	if err != nil {
		return nil, errors.New(
			web.GetTranslation(ctx, "fieldNotFound", map[string]string{
				"field": web.GetTranslation(ctx, "campaign", nil),
			}),
		)
	}
	if *sessionUser.VtuberId != camp.VtuberID {
		return nil, errors.New(
			web.GetTranslation(ctx, "unauthorizedVariantUpdate", nil),
		)
	}
	newImage, err := c.storageService.ValidateAndUploadFromTemp(ctx, &c2.Msg.Image, storagev1.ImageFileType, "campaign_variant", true, &variant.Image)
	if err != nil {
		return nil, validation.NewFieldError("image", err)
	}
	err = c.repo.UpdateCampaignVariantById(ctx, campaignvariantsqueries.UpdateCampaignVariantByIdParams{
		ID:          c2.Msg.Id,
		Description: c2.Msg.Description,
		Image:       newImage,
		MaxSub:      c2.Msg.MaxSub,
		Price:       c2.Msg.Price,
		Title:       c2.Msg.Title,
	})
	if err != nil {
		return nil, err
	}
	return connect.NewResponse(&campaignsv1.UpdateCampaignVariantByIdResponse{
		Success: true,
		Message: web.GetTranslation(ctx, "variantUpdated", nil),
	}), nil
}
