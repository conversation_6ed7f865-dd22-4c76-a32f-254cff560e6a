package users

import (
	"log"
	"net/http"
	"os"

	"github.com/markbates/goth"
	"github.com/markbates/goth/gothic"
	"github.com/markbates/goth/providers/facebook"
	"github.com/markbates/goth/providers/google"
	"github.com/nsp-inc/vtuber/packages/oauth/twitterv2"
)

type ProviderIndex struct {
	Providers    []string          `json:"providers"`
	ProvidersMap map[string]string `json:"providers_map"`
}

func init() {
	goth.UseProviders(
		google.New("888123260375-r2cf1odj95oakgtm0cjab30p6i17puk9.apps.googleusercontent.com", "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", "http://localhost:8080/oauth/v1/google/callback", "email", "profile"),
		twitterv2.New("*************************", "EoDKqJaZVBmUmtRub2zaaMIfvHJiMj4vX1MWpfHXR7BFS5h26V", "http://localhost:8080/oauth/v1/twitterv2/callback"),
		facebook.New(os.Getenv("FACEBOOK_KEY"), os.Getenv("FACEBOOK_SECRET"), "http://localhost:3000/auth/facebook/callback"),
	)
}

func HandleOauthRoutes(mux *http.ServeMux) {
	mux.HandleFunc("/oauth/v1/{provider}/signin", func(w http.ResponseWriter, r *http.Request) {
		gothic.BeginAuthHandler(w, r)
	})

	mux.HandleFunc("/oauth/v1/{provider}/callback", func(w http.ResponseWriter, r *http.Request) {
		// Handle the callback from Twitter OAuth v2
		user, err := gothic.CompleteUserAuth(w, r)
		if err != nil {
			log.Println("Error completing user auth:", err)
			http.Error(w, "Authentication failed", http.StatusInternalServerError)
			return
		}

		w.Write([]byte("Welcome " + user.Name + "Your email is " + user.Email))
	})
}
