package billing

import (
	"encoding/json"
	"time"
)

type VtuberUserSubscriptionDetails struct {
	VtuberId           int64     `json:"vtuber_id"`
	Amount             int32     `json:"amount"`
	SubscriptionId     int64     `json:"subscription_id"`
	UserSubscriptionId int64     `json:"user_subscription_id"`
	ExpiresAt          time.Time `json:"expires_at"`
	IsRenewal          bool      `json:"is_renewal"`
}

type BillingInfo struct {
	CardNumber     string  `json:"card_number"`
	CardExpiryDate string  `json:"card_expiry_date"`
	FullName       string  `json:"full_name"`
	Address1       string  `json:"address_1"`
	Address2       *string `json:"address_2"`
	City           string  `json:"city"`
	State          *string `json:"state"`
	Country        string  `json:"country"`
	PostalCode     string  `json:"postal_code"`
	CompanyName    *string `json:"company_name"`
	VatNumber      *string `json:"vat_number"`
}

type CampaignVariantSubscriptionDetails struct {
	CampaignVariantId int64 `json:"campaign_variant_id"`
	Amount            int32 `json:"amount"`
	CampaignId        int64 `json:"campaign_id"`
	VtuberId          int64 `json:"vtuber_id"`
}

type TransactionDetail struct {
	VtuberUserSubscription      *VtuberUserSubscriptionDetails      `json:"vtuber_user_subscription_details,omitempty"`
	CampaignVariantSubscription *CampaignVariantSubscriptionDetails `json:"campaign_variant_subscription_details,omitempty"`
	BillingInfo                 *BillingInfo                        `json:"billing_info,omitempty"`
}

func (t TransactionDetail) GetSubscriptionType() string {
	if t.VtuberUserSubscription != nil {
		return "vtuber_user_subscription"
	}
	if t.CampaignVariantSubscription != nil {
		return "campaign_variant_subscription"
	}
	return ""
}

func (t TransactionDetail) GetCreatorSubscriptionDetails() (bool, *VtuberUserSubscriptionDetails) {
	if t.VtuberUserSubscription != nil {
		return true, t.VtuberUserSubscription
	}
	return false, nil
}

func (t TransactionDetail) GetCampaignVariantSubscriptionDetails() (bool, *CampaignVariantSubscriptionDetails) {
	if t.CampaignVariantSubscription != nil {
		return true, t.CampaignVariantSubscription
	}
	return false, nil
}

func (t TransactionDetail) ToByte() ([]byte, error) {

	return json.Marshal(t)
}

func TransactionDetailsFromBytes(data []byte) (*TransactionDetail, error) {
	var transactionDetail TransactionDetail
	err := json.Unmarshal(data, &transactionDetail)
	if err != nil {
		return nil, err
	}
	return &transactionDetail, nil
}
