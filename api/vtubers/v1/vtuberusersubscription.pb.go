// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.36.6
// 	protoc        (unknown)
// source: vtubers/v1/vtuberusersubscription.proto

package vtubersv1

import (
	_ "github.com/nsp-inc/vtuber/api/authz/v1"
	v1 "github.com/nsp-inc/vtuber/api/shared/v1"
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	timestamppb "google.golang.org/protobuf/types/known/timestamppb"
	reflect "reflect"
	sync "sync"
	unsafe "unsafe"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

type AddVtuberUserSubscriptionRequest struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	VtuberPlanId  int64                  `protobuf:"varint,2,opt,name=vtuber_plan_id,json=vtuberPlanId,proto3" json:"vtuber_plan_id,omitempty" validate:"required"`  
	IsRecurring   bool                   `protobuf:"varint,3,opt,name=is_recurring,json=isRecurring,proto3" json:"is_recurring,omitempty"`
	BillingId     int64                  `protobuf:"varint,5,opt,name=billing_id,json=billingId,proto3" json:"billing_id,omitempty" validate:"required"`  
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *AddVtuberUserSubscriptionRequest) Reset() {
	*x = AddVtuberUserSubscriptionRequest{}
	mi := &file_vtubers_v1_vtuberusersubscription_proto_msgTypes[0]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *AddVtuberUserSubscriptionRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*AddVtuberUserSubscriptionRequest) ProtoMessage() {}

func (x *AddVtuberUserSubscriptionRequest) ProtoReflect() protoreflect.Message {
	mi := &file_vtubers_v1_vtuberusersubscription_proto_msgTypes[0]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use AddVtuberUserSubscriptionRequest.ProtoReflect.Descriptor instead.
func (*AddVtuberUserSubscriptionRequest) Descriptor() ([]byte, []int) {
	return file_vtubers_v1_vtuberusersubscription_proto_rawDescGZIP(), []int{0}
}

func (x *AddVtuberUserSubscriptionRequest) GetVtuberPlanId() int64 {
	if x != nil {
		return x.VtuberPlanId
	}
	return 0
}

func (x *AddVtuberUserSubscriptionRequest) GetIsRecurring() bool {
	if x != nil {
		return x.IsRecurring
	}
	return false
}

func (x *AddVtuberUserSubscriptionRequest) GetBillingId() int64 {
	if x != nil {
		return x.BillingId
	}
	return 0
}

type AddVtuberUserSubscriptionResponse struct {
	state         protoimpl.MessageState  `protogen:"open.v1"`
	Data          *VtuberUserSubscription `protobuf:"bytes,1,opt,name=data,proto3" json:"data,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *AddVtuberUserSubscriptionResponse) Reset() {
	*x = AddVtuberUserSubscriptionResponse{}
	mi := &file_vtubers_v1_vtuberusersubscription_proto_msgTypes[1]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *AddVtuberUserSubscriptionResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*AddVtuberUserSubscriptionResponse) ProtoMessage() {}

func (x *AddVtuberUserSubscriptionResponse) ProtoReflect() protoreflect.Message {
	mi := &file_vtubers_v1_vtuberusersubscription_proto_msgTypes[1]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use AddVtuberUserSubscriptionResponse.ProtoReflect.Descriptor instead.
func (*AddVtuberUserSubscriptionResponse) Descriptor() ([]byte, []int) {
	return file_vtubers_v1_vtuberusersubscription_proto_rawDescGZIP(), []int{1}
}

func (x *AddVtuberUserSubscriptionResponse) GetData() *VtuberUserSubscription {
	if x != nil {
		return x.Data
	}
	return nil
}

type VtuberUserSubscription struct {
	state                protoimpl.MessageState `protogen:"open.v1"`
	Id                   int64                  `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
	VtuberId             int64                  `protobuf:"varint,2,opt,name=vtuber_id,json=vtuberId,proto3" json:"vtuber_id,omitempty"`
	UserId               int64                  `protobuf:"varint,8,opt,name=user_id,json=userId,proto3" json:"user_id,omitempty"`
	User                 *User                  `protobuf:"bytes,3,opt,name=user,proto3" json:"user,omitempty"`
	VtuberSubscriptionId int64                  `protobuf:"varint,4,opt,name=vtuber_subscription_id,json=vtuberSubscriptionId,proto3" json:"vtuber_subscription_id,omitempty"`
	IsRecurring          bool                   `protobuf:"varint,5,opt,name=is_recurring,json=isRecurring,proto3" json:"is_recurring,omitempty"`
	CreatedAt            *timestamppb.Timestamp `protobuf:"bytes,6,opt,name=created_at,json=createdAt,proto3" json:"created_at,omitempty"`
	ExpiresOn            *timestamppb.Timestamp `protobuf:"bytes,7,opt,name=expires_on,json=expiresOn,proto3" json:"expires_on,omitempty"`
	unknownFields        protoimpl.UnknownFields
	sizeCache            protoimpl.SizeCache
}

func (x *VtuberUserSubscription) Reset() {
	*x = VtuberUserSubscription{}
	mi := &file_vtubers_v1_vtuberusersubscription_proto_msgTypes[2]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *VtuberUserSubscription) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*VtuberUserSubscription) ProtoMessage() {}

func (x *VtuberUserSubscription) ProtoReflect() protoreflect.Message {
	mi := &file_vtubers_v1_vtuberusersubscription_proto_msgTypes[2]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use VtuberUserSubscription.ProtoReflect.Descriptor instead.
func (*VtuberUserSubscription) Descriptor() ([]byte, []int) {
	return file_vtubers_v1_vtuberusersubscription_proto_rawDescGZIP(), []int{2}
}

func (x *VtuberUserSubscription) GetId() int64 {
	if x != nil {
		return x.Id
	}
	return 0
}

func (x *VtuberUserSubscription) GetVtuberId() int64 {
	if x != nil {
		return x.VtuberId
	}
	return 0
}

func (x *VtuberUserSubscription) GetUserId() int64 {
	if x != nil {
		return x.UserId
	}
	return 0
}

func (x *VtuberUserSubscription) GetUser() *User {
	if x != nil {
		return x.User
	}
	return nil
}

func (x *VtuberUserSubscription) GetVtuberSubscriptionId() int64 {
	if x != nil {
		return x.VtuberSubscriptionId
	}
	return 0
}

func (x *VtuberUserSubscription) GetIsRecurring() bool {
	if x != nil {
		return x.IsRecurring
	}
	return false
}

func (x *VtuberUserSubscription) GetCreatedAt() *timestamppb.Timestamp {
	if x != nil {
		return x.CreatedAt
	}
	return nil
}

func (x *VtuberUserSubscription) GetExpiresOn() *timestamppb.Timestamp {
	if x != nil {
		return x.ExpiresOn
	}
	return nil
}

type User struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Id            int64                  `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
	Email         string                 `protobuf:"bytes,2,opt,name=email,proto3" json:"email,omitempty"`
	Name          string                 `protobuf:"bytes,3,opt,name=name,proto3" json:"name,omitempty"`
	Image         *string                `protobuf:"bytes,4,opt,name=image,proto3,oneof" json:"image,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *User) Reset() {
	*x = User{}
	mi := &file_vtubers_v1_vtuberusersubscription_proto_msgTypes[3]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *User) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*User) ProtoMessage() {}

func (x *User) ProtoReflect() protoreflect.Message {
	mi := &file_vtubers_v1_vtuberusersubscription_proto_msgTypes[3]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use User.ProtoReflect.Descriptor instead.
func (*User) Descriptor() ([]byte, []int) {
	return file_vtubers_v1_vtuberusersubscription_proto_rawDescGZIP(), []int{3}
}

func (x *User) GetId() int64 {
	if x != nil {
		return x.Id
	}
	return 0
}

func (x *User) GetEmail() string {
	if x != nil {
		return x.Email
	}
	return ""
}

func (x *User) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *User) GetImage() string {
	if x != nil && x.Image != nil {
		return *x.Image
	}
	return ""
}

type GetAllVtuberUserSubscriptionsRequest struct {
	state                protoimpl.MessageState `protogen:"open.v1"`
	UserId               *int64                 `protobuf:"varint,1,opt,name=user_id,json=userId,proto3,oneof" json:"user_id,omitempty"`
	VtuberId             *int64                 `protobuf:"varint,2,opt,name=vtuber_id,json=vtuberId,proto3,oneof" json:"vtuber_id,omitempty"`
	VtuberSubscriptionId *int64                 `protobuf:"varint,3,opt,name=vtuber_subscription_id,json=vtuberSubscriptionId,proto3,oneof" json:"vtuber_subscription_id,omitempty"`
	Pagination           *v1.PaginationRequest  `protobuf:"bytes,4,opt,name=pagination,proto3,oneof" json:"pagination,omitempty"`
	unknownFields        protoimpl.UnknownFields
	sizeCache            protoimpl.SizeCache
}

func (x *GetAllVtuberUserSubscriptionsRequest) Reset() {
	*x = GetAllVtuberUserSubscriptionsRequest{}
	mi := &file_vtubers_v1_vtuberusersubscription_proto_msgTypes[4]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *GetAllVtuberUserSubscriptionsRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetAllVtuberUserSubscriptionsRequest) ProtoMessage() {}

func (x *GetAllVtuberUserSubscriptionsRequest) ProtoReflect() protoreflect.Message {
	mi := &file_vtubers_v1_vtuberusersubscription_proto_msgTypes[4]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetAllVtuberUserSubscriptionsRequest.ProtoReflect.Descriptor instead.
func (*GetAllVtuberUserSubscriptionsRequest) Descriptor() ([]byte, []int) {
	return file_vtubers_v1_vtuberusersubscription_proto_rawDescGZIP(), []int{4}
}

func (x *GetAllVtuberUserSubscriptionsRequest) GetUserId() int64 {
	if x != nil && x.UserId != nil {
		return *x.UserId
	}
	return 0
}

func (x *GetAllVtuberUserSubscriptionsRequest) GetVtuberId() int64 {
	if x != nil && x.VtuberId != nil {
		return *x.VtuberId
	}
	return 0
}

func (x *GetAllVtuberUserSubscriptionsRequest) GetVtuberSubscriptionId() int64 {
	if x != nil && x.VtuberSubscriptionId != nil {
		return *x.VtuberSubscriptionId
	}
	return 0
}

func (x *GetAllVtuberUserSubscriptionsRequest) GetPagination() *v1.PaginationRequest {
	if x != nil {
		return x.Pagination
	}
	return nil
}

type GetVtuberUserSubscriptionByIdRequest struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Id            int64                  `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty" validate:"required"`  
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *GetVtuberUserSubscriptionByIdRequest) Reset() {
	*x = GetVtuberUserSubscriptionByIdRequest{}
	mi := &file_vtubers_v1_vtuberusersubscription_proto_msgTypes[5]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *GetVtuberUserSubscriptionByIdRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetVtuberUserSubscriptionByIdRequest) ProtoMessage() {}

func (x *GetVtuberUserSubscriptionByIdRequest) ProtoReflect() protoreflect.Message {
	mi := &file_vtubers_v1_vtuberusersubscription_proto_msgTypes[5]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetVtuberUserSubscriptionByIdRequest.ProtoReflect.Descriptor instead.
func (*GetVtuberUserSubscriptionByIdRequest) Descriptor() ([]byte, []int) {
	return file_vtubers_v1_vtuberusersubscription_proto_rawDescGZIP(), []int{5}
}

func (x *GetVtuberUserSubscriptionByIdRequest) GetId() int64 {
	if x != nil {
		return x.Id
	}
	return 0
}

type GetAllVtuberUserSubscriptionsOfUserRequest struct {
	state                protoimpl.MessageState `protogen:"open.v1"`
	VtuberId             *int64                 `protobuf:"varint,2,opt,name=vtuber_id,json=vtuberId,proto3,oneof" json:"vtuber_id,omitempty"`
	VtuberSubscriptionId *int64                 `protobuf:"varint,3,opt,name=vtuber_subscription_id,json=vtuberSubscriptionId,proto3,oneof" json:"vtuber_subscription_id,omitempty"`
	unknownFields        protoimpl.UnknownFields
	sizeCache            protoimpl.SizeCache
}

func (x *GetAllVtuberUserSubscriptionsOfUserRequest) Reset() {
	*x = GetAllVtuberUserSubscriptionsOfUserRequest{}
	mi := &file_vtubers_v1_vtuberusersubscription_proto_msgTypes[6]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *GetAllVtuberUserSubscriptionsOfUserRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetAllVtuberUserSubscriptionsOfUserRequest) ProtoMessage() {}

func (x *GetAllVtuberUserSubscriptionsOfUserRequest) ProtoReflect() protoreflect.Message {
	mi := &file_vtubers_v1_vtuberusersubscription_proto_msgTypes[6]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetAllVtuberUserSubscriptionsOfUserRequest.ProtoReflect.Descriptor instead.
func (*GetAllVtuberUserSubscriptionsOfUserRequest) Descriptor() ([]byte, []int) {
	return file_vtubers_v1_vtuberusersubscription_proto_rawDescGZIP(), []int{6}
}

func (x *GetAllVtuberUserSubscriptionsOfUserRequest) GetVtuberId() int64 {
	if x != nil && x.VtuberId != nil {
		return *x.VtuberId
	}
	return 0
}

func (x *GetAllVtuberUserSubscriptionsOfUserRequest) GetVtuberSubscriptionId() int64 {
	if x != nil && x.VtuberSubscriptionId != nil {
		return *x.VtuberSubscriptionId
	}
	return 0
}

type GetAllVtuberUserSubscriptionsResponse struct {
	state              protoimpl.MessageState    `protogen:"open.v1"`
	Vtubersubscription []*VtuberUserSubscription `protobuf:"bytes,1,rep,name=vtubersubscription,proto3" json:"vtubersubscription,omitempty"`
	PaginationDetails  *v1.PaginationDetails     `protobuf:"bytes,2,opt,name=pagination_details,json=paginationDetails,proto3" json:"pagination_details,omitempty"`
	unknownFields      protoimpl.UnknownFields
	sizeCache          protoimpl.SizeCache
}

func (x *GetAllVtuberUserSubscriptionsResponse) Reset() {
	*x = GetAllVtuberUserSubscriptionsResponse{}
	mi := &file_vtubers_v1_vtuberusersubscription_proto_msgTypes[7]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *GetAllVtuberUserSubscriptionsResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetAllVtuberUserSubscriptionsResponse) ProtoMessage() {}

func (x *GetAllVtuberUserSubscriptionsResponse) ProtoReflect() protoreflect.Message {
	mi := &file_vtubers_v1_vtuberusersubscription_proto_msgTypes[7]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetAllVtuberUserSubscriptionsResponse.ProtoReflect.Descriptor instead.
func (*GetAllVtuberUserSubscriptionsResponse) Descriptor() ([]byte, []int) {
	return file_vtubers_v1_vtuberusersubscription_proto_rawDescGZIP(), []int{7}
}

func (x *GetAllVtuberUserSubscriptionsResponse) GetVtubersubscription() []*VtuberUserSubscription {
	if x != nil {
		return x.Vtubersubscription
	}
	return nil
}

func (x *GetAllVtuberUserSubscriptionsResponse) GetPaginationDetails() *v1.PaginationDetails {
	if x != nil {
		return x.PaginationDetails
	}
	return nil
}

type GetMyVtuberUserSubscriptionsRequest struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	SubId         *int64                 `protobuf:"varint,2,opt,name=sub_id,json=subId,proto3,oneof" json:"sub_id,omitempty"`
	Pagination    *v1.PaginationRequest  `protobuf:"bytes,1,opt,name=pagination,proto3,oneof" json:"pagination,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *GetMyVtuberUserSubscriptionsRequest) Reset() {
	*x = GetMyVtuberUserSubscriptionsRequest{}
	mi := &file_vtubers_v1_vtuberusersubscription_proto_msgTypes[8]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *GetMyVtuberUserSubscriptionsRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetMyVtuberUserSubscriptionsRequest) ProtoMessage() {}

func (x *GetMyVtuberUserSubscriptionsRequest) ProtoReflect() protoreflect.Message {
	mi := &file_vtubers_v1_vtuberusersubscription_proto_msgTypes[8]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetMyVtuberUserSubscriptionsRequest.ProtoReflect.Descriptor instead.
func (*GetMyVtuberUserSubscriptionsRequest) Descriptor() ([]byte, []int) {
	return file_vtubers_v1_vtuberusersubscription_proto_rawDescGZIP(), []int{8}
}

func (x *GetMyVtuberUserSubscriptionsRequest) GetSubId() int64 {
	if x != nil && x.SubId != nil {
		return *x.SubId
	}
	return 0
}

func (x *GetMyVtuberUserSubscriptionsRequest) GetPagination() *v1.PaginationRequest {
	if x != nil {
		return x.Pagination
	}
	return nil
}

type DeleteVtuberUserSubscriptionByIdRequest struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Id            int64                  `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty" validate:"required"`  
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *DeleteVtuberUserSubscriptionByIdRequest) Reset() {
	*x = DeleteVtuberUserSubscriptionByIdRequest{}
	mi := &file_vtubers_v1_vtuberusersubscription_proto_msgTypes[9]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *DeleteVtuberUserSubscriptionByIdRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DeleteVtuberUserSubscriptionByIdRequest) ProtoMessage() {}

func (x *DeleteVtuberUserSubscriptionByIdRequest) ProtoReflect() protoreflect.Message {
	mi := &file_vtubers_v1_vtuberusersubscription_proto_msgTypes[9]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DeleteVtuberUserSubscriptionByIdRequest.ProtoReflect.Descriptor instead.
func (*DeleteVtuberUserSubscriptionByIdRequest) Descriptor() ([]byte, []int) {
	return file_vtubers_v1_vtuberusersubscription_proto_rawDescGZIP(), []int{9}
}

func (x *DeleteVtuberUserSubscriptionByIdRequest) GetId() int64 {
	if x != nil {
		return x.Id
	}
	return 0
}

type UpdateVtuberUserSubscriptionRequest struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	IsRecurring   bool                   `protobuf:"varint,1,opt,name=is_recurring,json=isRecurring,proto3" json:"is_recurring,omitempty"`
	Id            int64                  `protobuf:"varint,3,opt,name=id,proto3" json:"id,omitempty" validate:"required"`  
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *UpdateVtuberUserSubscriptionRequest) Reset() {
	*x = UpdateVtuberUserSubscriptionRequest{}
	mi := &file_vtubers_v1_vtuberusersubscription_proto_msgTypes[10]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *UpdateVtuberUserSubscriptionRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UpdateVtuberUserSubscriptionRequest) ProtoMessage() {}

func (x *UpdateVtuberUserSubscriptionRequest) ProtoReflect() protoreflect.Message {
	mi := &file_vtubers_v1_vtuberusersubscription_proto_msgTypes[10]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UpdateVtuberUserSubscriptionRequest.ProtoReflect.Descriptor instead.
func (*UpdateVtuberUserSubscriptionRequest) Descriptor() ([]byte, []int) {
	return file_vtubers_v1_vtuberusersubscription_proto_rawDescGZIP(), []int{10}
}

func (x *UpdateVtuberUserSubscriptionRequest) GetIsRecurring() bool {
	if x != nil {
		return x.IsRecurring
	}
	return false
}

func (x *UpdateVtuberUserSubscriptionRequest) GetId() int64 {
	if x != nil {
		return x.Id
	}
	return 0
}

var File_vtubers_v1_vtuberusersubscription_proto protoreflect.FileDescriptor

const file_vtubers_v1_vtuberusersubscription_proto_rawDesc = "" +
	"\n" +
	"'vtubers/v1/vtuberusersubscription.proto\x12\x0eapi.vtubers.v1\x1a\x14authz/v1/authz.proto\x1a\x1fgoogle/protobuf/timestamp.proto\x1a\x17shared/v1/generic.proto\x1a\x1ashared/v1/pagination.proto\x1a\x17shared/v1/profile.proto\x1a\"shared/v1/social_media_links.proto\"\x8a\x01\n" +
	" AddVtuberUserSubscriptionRequest\x12$\n" +
	"\x0evtuber_plan_id\x18\x02 \x01(\x03R\fvtuberPlanId\x12!\n" +
	"\fis_recurring\x18\x03 \x01(\bR\visRecurring\x12\x1d\n" +
	"\n" +
	"billing_id\x18\x05 \x01(\x03R\tbillingId\"_\n" +
	"!AddVtuberUserSubscriptionResponse\x12:\n" +
	"\x04data\x18\x01 \x01(\v2&.api.vtubers.v1.VtuberUserSubscriptionR\x04data\"\xd7\x02\n" +
	"\x16VtuberUserSubscription\x12\x0e\n" +
	"\x02id\x18\x01 \x01(\x03R\x02id\x12\x1b\n" +
	"\tvtuber_id\x18\x02 \x01(\x03R\bvtuberId\x12\x17\n" +
	"\auser_id\x18\b \x01(\x03R\x06userId\x12(\n" +
	"\x04user\x18\x03 \x01(\v2\x14.api.vtubers.v1.UserR\x04user\x124\n" +
	"\x16vtuber_subscription_id\x18\x04 \x01(\x03R\x14vtuberSubscriptionId\x12!\n" +
	"\fis_recurring\x18\x05 \x01(\bR\visRecurring\x129\n" +
	"\n" +
	"created_at\x18\x06 \x01(\v2\x1a.google.protobuf.TimestampR\tcreatedAt\x129\n" +
	"\n" +
	"expires_on\x18\a \x01(\v2\x1a.google.protobuf.TimestampR\texpiresOn\"e\n" +
	"\x04User\x12\x0e\n" +
	"\x02id\x18\x01 \x01(\x03R\x02id\x12\x14\n" +
	"\x05email\x18\x02 \x01(\tR\x05email\x12\x12\n" +
	"\x04name\x18\x03 \x01(\tR\x04name\x12\x19\n" +
	"\x05image\x18\x04 \x01(\tH\x00R\x05image\x88\x01\x01B\b\n" +
	"\x06_image\"\xac\x02\n" +
	"$GetAllVtuberUserSubscriptionsRequest\x12\x1c\n" +
	"\auser_id\x18\x01 \x01(\x03H\x00R\x06userId\x88\x01\x01\x12 \n" +
	"\tvtuber_id\x18\x02 \x01(\x03H\x01R\bvtuberId\x88\x01\x01\x129\n" +
	"\x16vtuber_subscription_id\x18\x03 \x01(\x03H\x02R\x14vtuberSubscriptionId\x88\x01\x01\x12E\n" +
	"\n" +
	"pagination\x18\x04 \x01(\v2 .api.shared.v1.PaginationRequestH\x03R\n" +
	"pagination\x88\x01\x01B\n" +
	"\n" +
	"\b_user_idB\f\n" +
	"\n" +
	"_vtuber_idB\x19\n" +
	"\x17_vtuber_subscription_idB\r\n" +
	"\v_pagination\"6\n" +
	"$GetVtuberUserSubscriptionByIdRequest\x12\x0e\n" +
	"\x02id\x18\x01 \x01(\x03R\x02id\"\xb2\x01\n" +
	"*GetAllVtuberUserSubscriptionsOfUserRequest\x12 \n" +
	"\tvtuber_id\x18\x02 \x01(\x03H\x00R\bvtuberId\x88\x01\x01\x129\n" +
	"\x16vtuber_subscription_id\x18\x03 \x01(\x03H\x01R\x14vtuberSubscriptionId\x88\x01\x01B\f\n" +
	"\n" +
	"_vtuber_idB\x19\n" +
	"\x17_vtuber_subscription_id\"\xd0\x01\n" +
	"%GetAllVtuberUserSubscriptionsResponse\x12V\n" +
	"\x12vtubersubscription\x18\x01 \x03(\v2&.api.vtubers.v1.VtuberUserSubscriptionR\x12vtubersubscription\x12O\n" +
	"\x12pagination_details\x18\x02 \x01(\v2 .api.shared.v1.PaginationDetailsR\x11paginationDetails\"\xa2\x01\n" +
	"#GetMyVtuberUserSubscriptionsRequest\x12\x1a\n" +
	"\x06sub_id\x18\x02 \x01(\x03H\x00R\x05subId\x88\x01\x01\x12E\n" +
	"\n" +
	"pagination\x18\x01 \x01(\v2 .api.shared.v1.PaginationRequestH\x01R\n" +
	"pagination\x88\x01\x01B\t\n" +
	"\a_sub_idB\r\n" +
	"\v_pagination\"9\n" +
	"'DeleteVtuberUserSubscriptionByIdRequest\x12\x0e\n" +
	"\x02id\x18\x01 \x01(\x03R\x02id\"X\n" +
	"#UpdateVtuberUserSubscriptionRequest\x12!\n" +
	"\fis_recurring\x18\x01 \x01(\bR\visRecurring\x12\x0e\n" +
	"\x02id\x18\x03 \x01(\x03R\x02id2\xde\x06\n" +
	"\x1dVtuberUserSubscriptionService\x12\x88\x01\n" +
	"\x19AddVtuberUserSubscription\x120.api.vtubers.v1.AddVtuberUserSubscriptionRequest\x1a1.api.vtubers.v1.AddVtuberUserSubscriptionResponse\"\x06\x82\xb5\x18\x02\b\x01\x12\x96\x01\n" +
	"\x1dGetAllVtuberUserSubscriptions\x124.api.vtubers.v1.GetAllVtuberUserSubscriptionsRequest\x1a5.api.vtubers.v1.GetAllVtuberUserSubscriptionsResponse\"\b\x82\xb5\x18\x04\b\x01\x10\x01\x12}\n" +
	"\x1dGetVtuberUserSubscriptionById\x124.api.vtubers.v1.GetVtuberUserSubscriptionByIdRequest\x1a&.api.vtubers.v1.VtuberUserSubscription\x12\x83\x01\n" +
	" DeleteVtuberUserSubscriptionById\x127.api.vtubers.v1.DeleteVtuberUserSubscriptionByIdRequest\x1a\x1e.api.shared.v1.GenericResponse\"\x06\x82\xb5\x18\x02\b\x01\x12\x7f\n" +
	" UpdateVtuberUserSubscriptionById\x123.api.vtubers.v1.UpdateVtuberUserSubscriptionRequest\x1a\x1e.api.shared.v1.GenericResponse\"\x06\x82\xb5\x18\x02\b\x01\x12\x92\x01\n" +
	"\x1cGetMyVtuberUserSubscriptions\x123.api.vtubers.v1.GetMyVtuberUserSubscriptionsRequest\x1a5.api.vtubers.v1.GetAllVtuberUserSubscriptionsResponse\"\x06\x82\xb5\x18\x02\b\x01B4Z2github.com/nsp-inc/vtuber/api/vtubers/v1;vtubersv1b\x06proto3"

var (
	file_vtubers_v1_vtuberusersubscription_proto_rawDescOnce sync.Once
	file_vtubers_v1_vtuberusersubscription_proto_rawDescData []byte
)

func file_vtubers_v1_vtuberusersubscription_proto_rawDescGZIP() []byte {
	file_vtubers_v1_vtuberusersubscription_proto_rawDescOnce.Do(func() {
		file_vtubers_v1_vtuberusersubscription_proto_rawDescData = protoimpl.X.CompressGZIP(unsafe.Slice(unsafe.StringData(file_vtubers_v1_vtuberusersubscription_proto_rawDesc), len(file_vtubers_v1_vtuberusersubscription_proto_rawDesc)))
	})
	return file_vtubers_v1_vtuberusersubscription_proto_rawDescData
}

var file_vtubers_v1_vtuberusersubscription_proto_msgTypes = make([]protoimpl.MessageInfo, 11)
var file_vtubers_v1_vtuberusersubscription_proto_goTypes = []any{
	(*AddVtuberUserSubscriptionRequest)(nil),           // 0: api.vtubers.v1.AddVtuberUserSubscriptionRequest
	(*AddVtuberUserSubscriptionResponse)(nil),          // 1: api.vtubers.v1.AddVtuberUserSubscriptionResponse
	(*VtuberUserSubscription)(nil),                     // 2: api.vtubers.v1.VtuberUserSubscription
	(*User)(nil),                                       // 3: api.vtubers.v1.User
	(*GetAllVtuberUserSubscriptionsRequest)(nil),       // 4: api.vtubers.v1.GetAllVtuberUserSubscriptionsRequest
	(*GetVtuberUserSubscriptionByIdRequest)(nil),       // 5: api.vtubers.v1.GetVtuberUserSubscriptionByIdRequest
	(*GetAllVtuberUserSubscriptionsOfUserRequest)(nil), // 6: api.vtubers.v1.GetAllVtuberUserSubscriptionsOfUserRequest
	(*GetAllVtuberUserSubscriptionsResponse)(nil),      // 7: api.vtubers.v1.GetAllVtuberUserSubscriptionsResponse
	(*GetMyVtuberUserSubscriptionsRequest)(nil),        // 8: api.vtubers.v1.GetMyVtuberUserSubscriptionsRequest
	(*DeleteVtuberUserSubscriptionByIdRequest)(nil),    // 9: api.vtubers.v1.DeleteVtuberUserSubscriptionByIdRequest
	(*UpdateVtuberUserSubscriptionRequest)(nil),        // 10: api.vtubers.v1.UpdateVtuberUserSubscriptionRequest
	(*timestamppb.Timestamp)(nil),                      // 11: google.protobuf.Timestamp
	(*v1.PaginationRequest)(nil),                       // 12: api.shared.v1.PaginationRequest
	(*v1.PaginationDetails)(nil),                       // 13: api.shared.v1.PaginationDetails
	(*v1.GenericResponse)(nil),                         // 14: api.shared.v1.GenericResponse
}
var file_vtubers_v1_vtuberusersubscription_proto_depIdxs = []int32{
	2,  // 0: api.vtubers.v1.AddVtuberUserSubscriptionResponse.data:type_name -> api.vtubers.v1.VtuberUserSubscription
	3,  // 1: api.vtubers.v1.VtuberUserSubscription.user:type_name -> api.vtubers.v1.User
	11, // 2: api.vtubers.v1.VtuberUserSubscription.created_at:type_name -> google.protobuf.Timestamp
	11, // 3: api.vtubers.v1.VtuberUserSubscription.expires_on:type_name -> google.protobuf.Timestamp
	12, // 4: api.vtubers.v1.GetAllVtuberUserSubscriptionsRequest.pagination:type_name -> api.shared.v1.PaginationRequest
	2,  // 5: api.vtubers.v1.GetAllVtuberUserSubscriptionsResponse.vtubersubscription:type_name -> api.vtubers.v1.VtuberUserSubscription
	13, // 6: api.vtubers.v1.GetAllVtuberUserSubscriptionsResponse.pagination_details:type_name -> api.shared.v1.PaginationDetails
	12, // 7: api.vtubers.v1.GetMyVtuberUserSubscriptionsRequest.pagination:type_name -> api.shared.v1.PaginationRequest
	0,  // 8: api.vtubers.v1.VtuberUserSubscriptionService.AddVtuberUserSubscription:input_type -> api.vtubers.v1.AddVtuberUserSubscriptionRequest
	4,  // 9: api.vtubers.v1.VtuberUserSubscriptionService.GetAllVtuberUserSubscriptions:input_type -> api.vtubers.v1.GetAllVtuberUserSubscriptionsRequest
	5,  // 10: api.vtubers.v1.VtuberUserSubscriptionService.GetVtuberUserSubscriptionById:input_type -> api.vtubers.v1.GetVtuberUserSubscriptionByIdRequest
	9,  // 11: api.vtubers.v1.VtuberUserSubscriptionService.DeleteVtuberUserSubscriptionById:input_type -> api.vtubers.v1.DeleteVtuberUserSubscriptionByIdRequest
	10, // 12: api.vtubers.v1.VtuberUserSubscriptionService.UpdateVtuberUserSubscriptionById:input_type -> api.vtubers.v1.UpdateVtuberUserSubscriptionRequest
	8,  // 13: api.vtubers.v1.VtuberUserSubscriptionService.GetMyVtuberUserSubscriptions:input_type -> api.vtubers.v1.GetMyVtuberUserSubscriptionsRequest
	1,  // 14: api.vtubers.v1.VtuberUserSubscriptionService.AddVtuberUserSubscription:output_type -> api.vtubers.v1.AddVtuberUserSubscriptionResponse
	7,  // 15: api.vtubers.v1.VtuberUserSubscriptionService.GetAllVtuberUserSubscriptions:output_type -> api.vtubers.v1.GetAllVtuberUserSubscriptionsResponse
	2,  // 16: api.vtubers.v1.VtuberUserSubscriptionService.GetVtuberUserSubscriptionById:output_type -> api.vtubers.v1.VtuberUserSubscription
	14, // 17: api.vtubers.v1.VtuberUserSubscriptionService.DeleteVtuberUserSubscriptionById:output_type -> api.shared.v1.GenericResponse
	14, // 18: api.vtubers.v1.VtuberUserSubscriptionService.UpdateVtuberUserSubscriptionById:output_type -> api.shared.v1.GenericResponse
	7,  // 19: api.vtubers.v1.VtuberUserSubscriptionService.GetMyVtuberUserSubscriptions:output_type -> api.vtubers.v1.GetAllVtuberUserSubscriptionsResponse
	14, // [14:20] is the sub-list for method output_type
	8,  // [8:14] is the sub-list for method input_type
	8,  // [8:8] is the sub-list for extension type_name
	8,  // [8:8] is the sub-list for extension extendee
	0,  // [0:8] is the sub-list for field type_name
}

func init() { file_vtubers_v1_vtuberusersubscription_proto_init() }
func file_vtubers_v1_vtuberusersubscription_proto_init() {
	if File_vtubers_v1_vtuberusersubscription_proto != nil {
		return
	}
	file_vtubers_v1_vtuberusersubscription_proto_msgTypes[3].OneofWrappers = []any{}
	file_vtubers_v1_vtuberusersubscription_proto_msgTypes[4].OneofWrappers = []any{}
	file_vtubers_v1_vtuberusersubscription_proto_msgTypes[6].OneofWrappers = []any{}
	file_vtubers_v1_vtuberusersubscription_proto_msgTypes[8].OneofWrappers = []any{}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: unsafe.Slice(unsafe.StringData(file_vtubers_v1_vtuberusersubscription_proto_rawDesc), len(file_vtubers_v1_vtuberusersubscription_proto_rawDesc)),
			NumEnums:      0,
			NumMessages:   11,
			NumExtensions: 0,
			NumServices:   1,
		},
		GoTypes:           file_vtubers_v1_vtuberusersubscription_proto_goTypes,
		DependencyIndexes: file_vtubers_v1_vtuberusersubscription_proto_depIdxs,
		MessageInfos:      file_vtubers_v1_vtuberusersubscription_proto_msgTypes,
	}.Build()
	File_vtubers_v1_vtuberusersubscription_proto = out.File
	file_vtubers_v1_vtuberusersubscription_proto_goTypes = nil
	file_vtubers_v1_vtuberusersubscription_proto_depIdxs = nil
}
