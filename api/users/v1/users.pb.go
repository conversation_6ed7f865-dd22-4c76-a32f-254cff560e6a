// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.36.6
// 	protoc        (unknown)
// source: users/v1/users.proto

package usersv1

import (
	_ "github.com/nsp-inc/vtuber/api/authz/v1"
	v11 "github.com/nsp-inc/vtuber/api/shared/v1"
	v1 "github.com/nsp-inc/vtuber/api/vtubers/v1"
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	timestamppb "google.golang.org/protobuf/types/known/timestamppb"
	reflect "reflect"
	sync "sync"
	unsafe "unsafe"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

type GetUserPointRequest struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *GetUserPointRequest) Reset() {
	*x = GetUserPointRequest{}
	mi := &file_users_v1_users_proto_msgTypes[0]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *GetUserPointRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetUserPointRequest) ProtoMessage() {}

func (x *GetUserPointRequest) ProtoReflect() protoreflect.Message {
	mi := &file_users_v1_users_proto_msgTypes[0]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetUserPointRequest.ProtoReflect.Descriptor instead.
func (*GetUserPointRequest) Descriptor() ([]byte, []int) {
	return file_users_v1_users_proto_rawDescGZIP(), []int{0}
}

type GetUserResponse struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Point         float32                `protobuf:"fixed32,1,opt,name=point,proto3" json:"point,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *GetUserResponse) Reset() {
	*x = GetUserResponse{}
	mi := &file_users_v1_users_proto_msgTypes[1]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *GetUserResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetUserResponse) ProtoMessage() {}

func (x *GetUserResponse) ProtoReflect() protoreflect.Message {
	mi := &file_users_v1_users_proto_msgTypes[1]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetUserResponse.ProtoReflect.Descriptor instead.
func (*GetUserResponse) Descriptor() ([]byte, []int) {
	return file_users_v1_users_proto_rawDescGZIP(), []int{1}
}

func (x *GetUserResponse) GetPoint() float32 {
	if x != nil {
		return x.Point
	}
	return 0
}

type User struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Id            int64                  `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
	FullName      string                 `protobuf:"bytes,2,opt,name=full_name,json=fullName,proto3" json:"full_name,omitempty"`
	Email         string                 `protobuf:"bytes,3,opt,name=email,proto3" json:"email,omitempty"`
	Image         *string                `protobuf:"bytes,4,opt,name=image,proto3,oneof" json:"image,omitempty"`
	Role          string                 `protobuf:"bytes,5,opt,name=role,proto3" json:"role,omitempty"`
	EmailVerified bool                   `protobuf:"varint,6,opt,name=email_verified,json=emailVerified,proto3" json:"email_verified,omitempty"`
	Dob           *timestamppb.Timestamp `protobuf:"bytes,7,opt,name=dob,proto3" json:"dob,omitempty"`
	IsBanned      bool                   `protobuf:"varint,8,opt,name=is_banned,json=isBanned,proto3" json:"is_banned,omitempty"`
	CreatedAt     *timestamppb.Timestamp `protobuf:"bytes,9,opt,name=created_at,json=createdAt,proto3" json:"created_at,omitempty"`
	IsVtuber      bool                   `protobuf:"varint,10,opt,name=is_vtuber,json=isVtuber,proto3" json:"is_vtuber,omitempty"`
	Vtuber        *v1.VtuberProfile      `protobuf:"bytes,11,opt,name=vtuber,proto3,oneof" json:"vtuber,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *User) Reset() {
	*x = User{}
	mi := &file_users_v1_users_proto_msgTypes[2]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *User) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*User) ProtoMessage() {}

func (x *User) ProtoReflect() protoreflect.Message {
	mi := &file_users_v1_users_proto_msgTypes[2]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use User.ProtoReflect.Descriptor instead.
func (*User) Descriptor() ([]byte, []int) {
	return file_users_v1_users_proto_rawDescGZIP(), []int{2}
}

func (x *User) GetId() int64 {
	if x != nil {
		return x.Id
	}
	return 0
}

func (x *User) GetFullName() string {
	if x != nil {
		return x.FullName
	}
	return ""
}

func (x *User) GetEmail() string {
	if x != nil {
		return x.Email
	}
	return ""
}

func (x *User) GetImage() string {
	if x != nil && x.Image != nil {
		return *x.Image
	}
	return ""
}

func (x *User) GetRole() string {
	if x != nil {
		return x.Role
	}
	return ""
}

func (x *User) GetEmailVerified() bool {
	if x != nil {
		return x.EmailVerified
	}
	return false
}

func (x *User) GetDob() *timestamppb.Timestamp {
	if x != nil {
		return x.Dob
	}
	return nil
}

func (x *User) GetIsBanned() bool {
	if x != nil {
		return x.IsBanned
	}
	return false
}

func (x *User) GetCreatedAt() *timestamppb.Timestamp {
	if x != nil {
		return x.CreatedAt
	}
	return nil
}

func (x *User) GetIsVtuber() bool {
	if x != nil {
		return x.IsVtuber
	}
	return false
}

func (x *User) GetVtuber() *v1.VtuberProfile {
	if x != nil {
		return x.Vtuber
	}
	return nil
}

type UpdateUserResponse struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Data          *User                  `protobuf:"bytes,1,opt,name=data,proto3" json:"data,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *UpdateUserResponse) Reset() {
	*x = UpdateUserResponse{}
	mi := &file_users_v1_users_proto_msgTypes[3]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *UpdateUserResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UpdateUserResponse) ProtoMessage() {}

func (x *UpdateUserResponse) ProtoReflect() protoreflect.Message {
	mi := &file_users_v1_users_proto_msgTypes[3]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UpdateUserResponse.ProtoReflect.Descriptor instead.
func (*UpdateUserResponse) Descriptor() ([]byte, []int) {
	return file_users_v1_users_proto_rawDescGZIP(), []int{3}
}

func (x *UpdateUserResponse) GetData() *User {
	if x != nil {
		return x.Data
	}
	return nil
}

type GetAllUsersRequest struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Pagination    *v11.PaginationRequest `protobuf:"bytes,1,opt,name=pagination,proto3,oneof" json:"pagination,omitempty"`
	Email         *string                `protobuf:"bytes,2,opt,name=email,proto3,oneof" json:"email,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *GetAllUsersRequest) Reset() {
	*x = GetAllUsersRequest{}
	mi := &file_users_v1_users_proto_msgTypes[4]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *GetAllUsersRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetAllUsersRequest) ProtoMessage() {}

func (x *GetAllUsersRequest) ProtoReflect() protoreflect.Message {
	mi := &file_users_v1_users_proto_msgTypes[4]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetAllUsersRequest.ProtoReflect.Descriptor instead.
func (*GetAllUsersRequest) Descriptor() ([]byte, []int) {
	return file_users_v1_users_proto_rawDescGZIP(), []int{4}
}

func (x *GetAllUsersRequest) GetPagination() *v11.PaginationRequest {
	if x != nil {
		return x.Pagination
	}
	return nil
}

func (x *GetAllUsersRequest) GetEmail() string {
	if x != nil && x.Email != nil {
		return *x.Email
	}
	return ""
}

type GetAllUsersResponse struct {
	state             protoimpl.MessageState `protogen:"open.v1"`
	Data              []*User                `protobuf:"bytes,1,rep,name=data,proto3" json:"data,omitempty"`
	PaginationDetails *v11.PaginationDetails `protobuf:"bytes,2,opt,name=pagination_details,json=paginationDetails,proto3" json:"pagination_details,omitempty"`
	unknownFields     protoimpl.UnknownFields
	sizeCache         protoimpl.SizeCache
}

func (x *GetAllUsersResponse) Reset() {
	*x = GetAllUsersResponse{}
	mi := &file_users_v1_users_proto_msgTypes[5]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *GetAllUsersResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetAllUsersResponse) ProtoMessage() {}

func (x *GetAllUsersResponse) ProtoReflect() protoreflect.Message {
	mi := &file_users_v1_users_proto_msgTypes[5]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetAllUsersResponse.ProtoReflect.Descriptor instead.
func (*GetAllUsersResponse) Descriptor() ([]byte, []int) {
	return file_users_v1_users_proto_rawDescGZIP(), []int{5}
}

func (x *GetAllUsersResponse) GetData() []*User {
	if x != nil {
		return x.Data
	}
	return nil
}

func (x *GetAllUsersResponse) GetPaginationDetails() *v11.PaginationDetails {
	if x != nil {
		return x.PaginationDetails
	}
	return nil
}

type GetAllDeletedUsersRequest struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Pagination    *v11.PaginationRequest `protobuf:"bytes,1,opt,name=pagination,proto3,oneof" json:"pagination,omitempty"`
	Email         *string                `protobuf:"bytes,2,opt,name=email,proto3,oneof" json:"email,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *GetAllDeletedUsersRequest) Reset() {
	*x = GetAllDeletedUsersRequest{}
	mi := &file_users_v1_users_proto_msgTypes[6]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *GetAllDeletedUsersRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetAllDeletedUsersRequest) ProtoMessage() {}

func (x *GetAllDeletedUsersRequest) ProtoReflect() protoreflect.Message {
	mi := &file_users_v1_users_proto_msgTypes[6]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetAllDeletedUsersRequest.ProtoReflect.Descriptor instead.
func (*GetAllDeletedUsersRequest) Descriptor() ([]byte, []int) {
	return file_users_v1_users_proto_rawDescGZIP(), []int{6}
}

func (x *GetAllDeletedUsersRequest) GetPagination() *v11.PaginationRequest {
	if x != nil {
		return x.Pagination
	}
	return nil
}

func (x *GetAllDeletedUsersRequest) GetEmail() string {
	if x != nil && x.Email != nil {
		return *x.Email
	}
	return ""
}

type GetAllDeletedUsersResponse struct {
	state             protoimpl.MessageState `protogen:"open.v1"`
	Data              []*User                `protobuf:"bytes,1,rep,name=data,proto3" json:"data,omitempty"`
	PaginationDetails *v11.PaginationDetails `protobuf:"bytes,2,opt,name=pagination_details,json=paginationDetails,proto3" json:"pagination_details,omitempty"`
	unknownFields     protoimpl.UnknownFields
	sizeCache         protoimpl.SizeCache
}

func (x *GetAllDeletedUsersResponse) Reset() {
	*x = GetAllDeletedUsersResponse{}
	mi := &file_users_v1_users_proto_msgTypes[7]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *GetAllDeletedUsersResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetAllDeletedUsersResponse) ProtoMessage() {}

func (x *GetAllDeletedUsersResponse) ProtoReflect() protoreflect.Message {
	mi := &file_users_v1_users_proto_msgTypes[7]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetAllDeletedUsersResponse.ProtoReflect.Descriptor instead.
func (*GetAllDeletedUsersResponse) Descriptor() ([]byte, []int) {
	return file_users_v1_users_proto_rawDescGZIP(), []int{7}
}

func (x *GetAllDeletedUsersResponse) GetData() []*User {
	if x != nil {
		return x.Data
	}
	return nil
}

func (x *GetAllDeletedUsersResponse) GetPaginationDetails() *v11.PaginationDetails {
	if x != nil {
		return x.PaginationDetails
	}
	return nil
}

type GetUserByIdRequest struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Id            int64                  `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty" validate:"required"`  
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *GetUserByIdRequest) Reset() {
	*x = GetUserByIdRequest{}
	mi := &file_users_v1_users_proto_msgTypes[8]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *GetUserByIdRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetUserByIdRequest) ProtoMessage() {}

func (x *GetUserByIdRequest) ProtoReflect() protoreflect.Message {
	mi := &file_users_v1_users_proto_msgTypes[8]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetUserByIdRequest.ProtoReflect.Descriptor instead.
func (*GetUserByIdRequest) Descriptor() ([]byte, []int) {
	return file_users_v1_users_proto_rawDescGZIP(), []int{8}
}

func (x *GetUserByIdRequest) GetId() int64 {
	if x != nil {
		return x.Id
	}
	return 0
}

type GetUserByIdResponse struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Data          *User                  `protobuf:"bytes,1,opt,name=data,proto3" json:"data,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *GetUserByIdResponse) Reset() {
	*x = GetUserByIdResponse{}
	mi := &file_users_v1_users_proto_msgTypes[9]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *GetUserByIdResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetUserByIdResponse) ProtoMessage() {}

func (x *GetUserByIdResponse) ProtoReflect() protoreflect.Message {
	mi := &file_users_v1_users_proto_msgTypes[9]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetUserByIdResponse.ProtoReflect.Descriptor instead.
func (*GetUserByIdResponse) Descriptor() ([]byte, []int) {
	return file_users_v1_users_proto_rawDescGZIP(), []int{9}
}

func (x *GetUserByIdResponse) GetData() *User {
	if x != nil {
		return x.Data
	}
	return nil
}

type DeleteUserByIdRequest struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Id            int64                  `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty" validate:"required"`  
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *DeleteUserByIdRequest) Reset() {
	*x = DeleteUserByIdRequest{}
	mi := &file_users_v1_users_proto_msgTypes[10]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *DeleteUserByIdRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DeleteUserByIdRequest) ProtoMessage() {}

func (x *DeleteUserByIdRequest) ProtoReflect() protoreflect.Message {
	mi := &file_users_v1_users_proto_msgTypes[10]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DeleteUserByIdRequest.ProtoReflect.Descriptor instead.
func (*DeleteUserByIdRequest) Descriptor() ([]byte, []int) {
	return file_users_v1_users_proto_rawDescGZIP(), []int{10}
}

func (x *DeleteUserByIdRequest) GetId() int64 {
	if x != nil {
		return x.Id
	}
	return 0
}

type UpdateUserByIdRequest struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	FullName      string                 `protobuf:"bytes,1,opt,name=full_name,json=fullName,proto3" json:"full_name,omitempty" validate:"required"`  
	Image         string                 `protobuf:"bytes,2,opt,name=image,proto3" json:"image,omitempty"`
	Id            int64                  `protobuf:"varint,3,opt,name=id,proto3" json:"id,omitempty" validate:"required"`  
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *UpdateUserByIdRequest) Reset() {
	*x = UpdateUserByIdRequest{}
	mi := &file_users_v1_users_proto_msgTypes[11]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *UpdateUserByIdRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UpdateUserByIdRequest) ProtoMessage() {}

func (x *UpdateUserByIdRequest) ProtoReflect() protoreflect.Message {
	mi := &file_users_v1_users_proto_msgTypes[11]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UpdateUserByIdRequest.ProtoReflect.Descriptor instead.
func (*UpdateUserByIdRequest) Descriptor() ([]byte, []int) {
	return file_users_v1_users_proto_rawDescGZIP(), []int{11}
}

func (x *UpdateUserByIdRequest) GetFullName() string {
	if x != nil {
		return x.FullName
	}
	return ""
}

func (x *UpdateUserByIdRequest) GetImage() string {
	if x != nil {
		return x.Image
	}
	return ""
}

func (x *UpdateUserByIdRequest) GetId() int64 {
	if x != nil {
		return x.Id
	}
	return 0
}

type BanUserRequest struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Id            int64                  `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty" validate:"required"`  
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *BanUserRequest) Reset() {
	*x = BanUserRequest{}
	mi := &file_users_v1_users_proto_msgTypes[12]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *BanUserRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*BanUserRequest) ProtoMessage() {}

func (x *BanUserRequest) ProtoReflect() protoreflect.Message {
	mi := &file_users_v1_users_proto_msgTypes[12]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use BanUserRequest.ProtoReflect.Descriptor instead.
func (*BanUserRequest) Descriptor() ([]byte, []int) {
	return file_users_v1_users_proto_rawDescGZIP(), []int{12}
}

func (x *BanUserRequest) GetId() int64 {
	if x != nil {
		return x.Id
	}
	return 0
}

var File_users_v1_users_proto protoreflect.FileDescriptor

const file_users_v1_users_proto_rawDesc = "" +
	"\n" +
	"\x14users/v1/users.proto\x12\fapi.users.v1\x1a\x14authz/v1/authz.proto\x1a\x1fgoogle/protobuf/timestamp.proto\x1a\x17shared/v1/generic.proto\x1a\x1ashared/v1/pagination.proto\x1a\x17shared/v1/profile.proto\x1a\"shared/v1/social_media_links.proto\x1a\x1fvtubers/v1/vtuberprofiles.proto\"\x15\n" +
	"\x13GetUserPointRequest\"'\n" +
	"\x0fGetUserResponse\x12\x14\n" +
	"\x05point\x18\x01 \x01(\x02R\x05point\"\x93\x03\n" +
	"\x04User\x12\x0e\n" +
	"\x02id\x18\x01 \x01(\x03R\x02id\x12\x1b\n" +
	"\tfull_name\x18\x02 \x01(\tR\bfullName\x12\x14\n" +
	"\x05email\x18\x03 \x01(\tR\x05email\x12\x19\n" +
	"\x05image\x18\x04 \x01(\tH\x00R\x05image\x88\x01\x01\x12\x12\n" +
	"\x04role\x18\x05 \x01(\tR\x04role\x12%\n" +
	"\x0eemail_verified\x18\x06 \x01(\bR\remailVerified\x12,\n" +
	"\x03dob\x18\a \x01(\v2\x1a.google.protobuf.TimestampR\x03dob\x12\x1b\n" +
	"\tis_banned\x18\b \x01(\bR\bisBanned\x129\n" +
	"\n" +
	"created_at\x18\t \x01(\v2\x1a.google.protobuf.TimestampR\tcreatedAt\x12\x1b\n" +
	"\tis_vtuber\x18\n" +
	" \x01(\bR\bisVtuber\x12:\n" +
	"\x06vtuber\x18\v \x01(\v2\x1d.api.vtubers.v1.VtuberProfileH\x01R\x06vtuber\x88\x01\x01B\b\n" +
	"\x06_imageB\t\n" +
	"\a_vtuber\"<\n" +
	"\x12UpdateUserResponse\x12&\n" +
	"\x04data\x18\x01 \x01(\v2\x12.api.users.v1.UserR\x04data\"\x8f\x01\n" +
	"\x12GetAllUsersRequest\x12E\n" +
	"\n" +
	"pagination\x18\x01 \x01(\v2 .api.shared.v1.PaginationRequestH\x00R\n" +
	"pagination\x88\x01\x01\x12\x19\n" +
	"\x05email\x18\x02 \x01(\tH\x01R\x05email\x88\x01\x01B\r\n" +
	"\v_paginationB\b\n" +
	"\x06_email\"\x8e\x01\n" +
	"\x13GetAllUsersResponse\x12&\n" +
	"\x04data\x18\x01 \x03(\v2\x12.api.users.v1.UserR\x04data\x12O\n" +
	"\x12pagination_details\x18\x02 \x01(\v2 .api.shared.v1.PaginationDetailsR\x11paginationDetails\"\x96\x01\n" +
	"\x19GetAllDeletedUsersRequest\x12E\n" +
	"\n" +
	"pagination\x18\x01 \x01(\v2 .api.shared.v1.PaginationRequestH\x00R\n" +
	"pagination\x88\x01\x01\x12\x19\n" +
	"\x05email\x18\x02 \x01(\tH\x01R\x05email\x88\x01\x01B\r\n" +
	"\v_paginationB\b\n" +
	"\x06_email\"\x95\x01\n" +
	"\x1aGetAllDeletedUsersResponse\x12&\n" +
	"\x04data\x18\x01 \x03(\v2\x12.api.users.v1.UserR\x04data\x12O\n" +
	"\x12pagination_details\x18\x02 \x01(\v2 .api.shared.v1.PaginationDetailsR\x11paginationDetails\"$\n" +
	"\x12GetUserByIdRequest\x12\x0e\n" +
	"\x02id\x18\x01 \x01(\x03R\x02id\"=\n" +
	"\x13GetUserByIdResponse\x12&\n" +
	"\x04data\x18\x01 \x01(\v2\x12.api.users.v1.UserR\x04data\"'\n" +
	"\x15DeleteUserByIdRequest\x12\x0e\n" +
	"\x02id\x18\x01 \x01(\x03R\x02id\"Z\n" +
	"\x15UpdateUserByIdRequest\x12\x1b\n" +
	"\tfull_name\x18\x01 \x01(\tR\bfullName\x12\x14\n" +
	"\x05image\x18\x02 \x01(\tR\x05image\x12\x0e\n" +
	"\x02id\x18\x03 \x01(\x03R\x02id\" \n" +
	"\x0eBanUserRequest\x12\x0e\n" +
	"\x02id\x18\x01 \x01(\x03R\x02id2\xb7\x06\n" +
	"\vUserService\x12\\\n" +
	"\vGetAllUsers\x12 .api.users.v1.GetAllUsersRequest\x1a!.api.users.v1.GetAllUsersResponse\"\b\x82\xb5\x18\x04\b\x01\x10\x01\x12q\n" +
	"\x12GetAllDeletedUsers\x12'.api.users.v1.GetAllDeletedUsersRequest\x1a(.api.users.v1.GetAllDeletedUsersResponse\"\b\x82\xb5\x18\x04\b\x01\x10\x01\x12Z\n" +
	"\vGetUserById\x12 .api.users.v1.GetUserByIdRequest\x1a!.api.users.v1.GetUserByIdResponse\"\x06\x82\xb5\x18\x02\b\x01\x12_\n" +
	"\x0eDeleteUserById\x12#.api.users.v1.DeleteUserByIdRequest\x1a\x1e.api.shared.v1.GenericResponse\"\b\x82\xb5\x18\x04\b\x01\x10\x01\x12\x88\x01\n" +
	"\x0eUpdateUserById\x12#.api.users.v1.UpdateUserByIdRequest\x1a .api.users.v1.UpdateUserResponse\"/\x82\xb5\x18+\b\x01\"'_user.role == 'admin' || _user.id == id\x12Q\n" +
	"\aBanUser\x12\x1c.api.users.v1.BanUserRequest\x1a\x1e.api.shared.v1.GenericResponse\"\b\x82\xb5\x18\x04\b\x01\x10\x01\x12b\n" +
	"\x11GetAllBannedUsers\x12 .api.users.v1.GetAllUsersRequest\x1a!.api.users.v1.GetAllUsersResponse\"\b\x82\xb5\x18\x04\b\x01\x10\x01\x12X\n" +
	"\fGetUserPoint\x12!.api.users.v1.GetUserPointRequest\x1a\x1d.api.users.v1.GetUserResponse\"\x06\x82\xb5\x18\x02\b\x01B0Z.github.com/nsp-inc/vtuber/api/users/v1;usersv1b\x06proto3"

var (
	file_users_v1_users_proto_rawDescOnce sync.Once
	file_users_v1_users_proto_rawDescData []byte
)

func file_users_v1_users_proto_rawDescGZIP() []byte {
	file_users_v1_users_proto_rawDescOnce.Do(func() {
		file_users_v1_users_proto_rawDescData = protoimpl.X.CompressGZIP(unsafe.Slice(unsafe.StringData(file_users_v1_users_proto_rawDesc), len(file_users_v1_users_proto_rawDesc)))
	})
	return file_users_v1_users_proto_rawDescData
}

var file_users_v1_users_proto_msgTypes = make([]protoimpl.MessageInfo, 13)
var file_users_v1_users_proto_goTypes = []any{
	(*GetUserPointRequest)(nil),        // 0: api.users.v1.GetUserPointRequest
	(*GetUserResponse)(nil),            // 1: api.users.v1.GetUserResponse
	(*User)(nil),                       // 2: api.users.v1.User
	(*UpdateUserResponse)(nil),         // 3: api.users.v1.UpdateUserResponse
	(*GetAllUsersRequest)(nil),         // 4: api.users.v1.GetAllUsersRequest
	(*GetAllUsersResponse)(nil),        // 5: api.users.v1.GetAllUsersResponse
	(*GetAllDeletedUsersRequest)(nil),  // 6: api.users.v1.GetAllDeletedUsersRequest
	(*GetAllDeletedUsersResponse)(nil), // 7: api.users.v1.GetAllDeletedUsersResponse
	(*GetUserByIdRequest)(nil),         // 8: api.users.v1.GetUserByIdRequest
	(*GetUserByIdResponse)(nil),        // 9: api.users.v1.GetUserByIdResponse
	(*DeleteUserByIdRequest)(nil),      // 10: api.users.v1.DeleteUserByIdRequest
	(*UpdateUserByIdRequest)(nil),      // 11: api.users.v1.UpdateUserByIdRequest
	(*BanUserRequest)(nil),             // 12: api.users.v1.BanUserRequest
	(*timestamppb.Timestamp)(nil),      // 13: google.protobuf.Timestamp
	(*v1.VtuberProfile)(nil),           // 14: api.vtubers.v1.VtuberProfile
	(*v11.PaginationRequest)(nil),      // 15: api.shared.v1.PaginationRequest
	(*v11.PaginationDetails)(nil),      // 16: api.shared.v1.PaginationDetails
	(*v11.GenericResponse)(nil),        // 17: api.shared.v1.GenericResponse
}
var file_users_v1_users_proto_depIdxs = []int32{
	13, // 0: api.users.v1.User.dob:type_name -> google.protobuf.Timestamp
	13, // 1: api.users.v1.User.created_at:type_name -> google.protobuf.Timestamp
	14, // 2: api.users.v1.User.vtuber:type_name -> api.vtubers.v1.VtuberProfile
	2,  // 3: api.users.v1.UpdateUserResponse.data:type_name -> api.users.v1.User
	15, // 4: api.users.v1.GetAllUsersRequest.pagination:type_name -> api.shared.v1.PaginationRequest
	2,  // 5: api.users.v1.GetAllUsersResponse.data:type_name -> api.users.v1.User
	16, // 6: api.users.v1.GetAllUsersResponse.pagination_details:type_name -> api.shared.v1.PaginationDetails
	15, // 7: api.users.v1.GetAllDeletedUsersRequest.pagination:type_name -> api.shared.v1.PaginationRequest
	2,  // 8: api.users.v1.GetAllDeletedUsersResponse.data:type_name -> api.users.v1.User
	16, // 9: api.users.v1.GetAllDeletedUsersResponse.pagination_details:type_name -> api.shared.v1.PaginationDetails
	2,  // 10: api.users.v1.GetUserByIdResponse.data:type_name -> api.users.v1.User
	4,  // 11: api.users.v1.UserService.GetAllUsers:input_type -> api.users.v1.GetAllUsersRequest
	6,  // 12: api.users.v1.UserService.GetAllDeletedUsers:input_type -> api.users.v1.GetAllDeletedUsersRequest
	8,  // 13: api.users.v1.UserService.GetUserById:input_type -> api.users.v1.GetUserByIdRequest
	10, // 14: api.users.v1.UserService.DeleteUserById:input_type -> api.users.v1.DeleteUserByIdRequest
	11, // 15: api.users.v1.UserService.UpdateUserById:input_type -> api.users.v1.UpdateUserByIdRequest
	12, // 16: api.users.v1.UserService.BanUser:input_type -> api.users.v1.BanUserRequest
	4,  // 17: api.users.v1.UserService.GetAllBannedUsers:input_type -> api.users.v1.GetAllUsersRequest
	0,  // 18: api.users.v1.UserService.GetUserPoint:input_type -> api.users.v1.GetUserPointRequest
	5,  // 19: api.users.v1.UserService.GetAllUsers:output_type -> api.users.v1.GetAllUsersResponse
	7,  // 20: api.users.v1.UserService.GetAllDeletedUsers:output_type -> api.users.v1.GetAllDeletedUsersResponse
	9,  // 21: api.users.v1.UserService.GetUserById:output_type -> api.users.v1.GetUserByIdResponse
	17, // 22: api.users.v1.UserService.DeleteUserById:output_type -> api.shared.v1.GenericResponse
	3,  // 23: api.users.v1.UserService.UpdateUserById:output_type -> api.users.v1.UpdateUserResponse
	17, // 24: api.users.v1.UserService.BanUser:output_type -> api.shared.v1.GenericResponse
	5,  // 25: api.users.v1.UserService.GetAllBannedUsers:output_type -> api.users.v1.GetAllUsersResponse
	1,  // 26: api.users.v1.UserService.GetUserPoint:output_type -> api.users.v1.GetUserResponse
	19, // [19:27] is the sub-list for method output_type
	11, // [11:19] is the sub-list for method input_type
	11, // [11:11] is the sub-list for extension type_name
	11, // [11:11] is the sub-list for extension extendee
	0,  // [0:11] is the sub-list for field type_name
}

func init() { file_users_v1_users_proto_init() }
func file_users_v1_users_proto_init() {
	if File_users_v1_users_proto != nil {
		return
	}
	file_users_v1_users_proto_msgTypes[2].OneofWrappers = []any{}
	file_users_v1_users_proto_msgTypes[4].OneofWrappers = []any{}
	file_users_v1_users_proto_msgTypes[6].OneofWrappers = []any{}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: unsafe.Slice(unsafe.StringData(file_users_v1_users_proto_rawDesc), len(file_users_v1_users_proto_rawDesc)),
			NumEnums:      0,
			NumMessages:   13,
			NumExtensions: 0,
			NumServices:   1,
		},
		GoTypes:           file_users_v1_users_proto_goTypes,
		DependencyIndexes: file_users_v1_users_proto_depIdxs,
		MessageInfos:      file_users_v1_users_proto_msgTypes,
	}.Build()
	File_users_v1_users_proto = out.File
	file_users_v1_users_proto_goTypes = nil
	file_users_v1_users_proto_depIdxs = nil
}
