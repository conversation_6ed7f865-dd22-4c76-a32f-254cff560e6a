syntax = "proto3";

package api.users.v1;

import "authz/v1/authz.proto";
import "google/protobuf/timestamp.proto";
import "shared/v1/generic.proto";
import "shared/v1/pagination.proto";
import "shared/v1/profile.proto";
import "shared/v1/social_media_links.proto";
import "vtubers/v1/vtuberprofiles.proto";

option go_package = "github.com/nsp-inc/vtuber/api/users/v1;usersv1";

//message AddUserRequest {
//  string full_name = 1; // @gotag: validate:"required"
//  string email = 2; // @gotag: validate:"required"
//  optional string image = 3;
//  string password = 4; // @gotag: validate:"required"
//  string role = 5; // @gotag: validate:"required"
//}

//message AddUserResponse {
//  User data = 2;
//}

message GetUserPointRequest {}

message GetUserResponse {
  float point = 1;
}

message User {
  int64 id = 1;
  string full_name = 2;
  string email = 3;
  optional string image = 4;
  string role = 5;
  bool email_verified = 6;
  google.protobuf.Timestamp dob = 7;
  bool is_banned = 8;
  google.protobuf.Timestamp created_at = 9;
  bool is_vtuber = 10;
  optional api.vtubers.v1.VtuberProfile vtuber = 11;
}

message UpdateUserResponse {
  User data = 1;
}

message GetAllUsersRequest {
  optional api.shared.v1.PaginationRequest pagination = 1;
  optional string email = 2;
}
message GetAllUsersResponse {
  repeated User data = 1;
  api.shared.v1.PaginationDetails pagination_details = 2;
}

message GetAllDeletedUsersRequest {
  optional api.shared.v1.PaginationRequest pagination = 1;
  optional string email = 2;
}
message GetAllDeletedUsersResponse {
  repeated User data = 1;
  api.shared.v1.PaginationDetails pagination_details = 2;
}

message GetUserByIdRequest {
  int64 id = 1; // @gotag: validate:"required"
}
message GetUserByIdResponse {
  User data = 1;
}

message DeleteUserByIdRequest {
  int64 id = 1; // @gotag: validate:"required"
}

message UpdateUserByIdRequest {
  string full_name = 1; // @gotag: validate:"required"
  string image = 2;
  int64 id = 3; // @gotag: validate:"required"
}

message BanUserRequest {
  int64 id = 1; // @gotag: validate:"required"
}
service UserService {
  //  rpc AddUser(AddUserRequest) returns (AddUserResponse);
  rpc GetAllUsers(GetAllUsersRequest) returns (GetAllUsersResponse) {
    option (api.authz.v1.options) = {
      require: true
      is_admin: true
    };
  }
  rpc GetAllDeletedUsers(GetAllDeletedUsersRequest) returns (GetAllDeletedUsersResponse) {
    option (api.authz.v1.options) = {
      require: true
      is_admin: true
    };
  }
  rpc GetUserById(GetUserByIdRequest) returns (GetUserByIdResponse) {
    option (api.authz.v1.options) = {require: true};
  }
  rpc DeleteUserById(DeleteUserByIdRequest) returns (api.shared.v1.GenericResponse) {
    option (api.authz.v1.options) = {
      require: true
      is_admin: true
    };
  }
  rpc UpdateUserById(UpdateUserByIdRequest) returns (UpdateUserResponse) {
    option (api.authz.v1.options) = {
      require: true
      expression: "_user.role == 'admin' || _user.id == id"
    };
  }
  rpc BanUser(BanUserRequest) returns (api.shared.v1.GenericResponse) {
    option (api.authz.v1.options) = {
      require: true
      is_admin: true
    };
  }
  rpc GetAllBannedUsers(GetAllUsersRequest) returns (GetAllUsersResponse) {
    option (api.authz.v1.options) = {
      require: true
      is_admin: true
    };
  }
  rpc GetUserPoint(GetUserPointRequest) returns (GetUserResponse) {
    option (api.authz.v1.options) = {require: true};
  }
}
