syntax = "proto3";

package api.vtubers.v1;

import "authz/v1/authz.proto";
import "google/protobuf/timestamp.proto";
import "shared/v1/generic.proto";
import "shared/v1/pagination.proto";
import "shared/v1/profile.proto";
import "shared/v1/social_media_links.proto";

option go_package = "github.com/nsp-inc/vtuber/api/vtubers/v1;vtubersv1";

message AddVtuberUserSubscriptionRequest {
  int64 vtuber_plan_id = 2; // @gotag: validate:"required"
  bool is_recurring = 3;
  int64 billing_id = 5; // @gotag: validate:"required"
}

message AddVtuberUserSubscriptionResponse {
  VtuberUserSubscription data = 1;
}

message VtuberUserSubscription {
  int64 id = 1;
  int64 vtuber_id = 2;
  int64 user_id = 8;
  User user = 3;
  int64 vtuber_subscription_id = 4;
  bool is_recurring = 5;
  google.protobuf.Timestamp created_at = 6;
  google.protobuf.Timestamp expires_on = 7;
}

message User {
  int64 id = 1;
  string email = 2;
  string name = 3;
  optional string image = 4;
}

message GetAllVtuberUserSubscriptionsRequest {
  optional int64 user_id = 1;
  optional int64 vtuber_id = 2;
  optional int64 vtuber_subscription_id = 3;
  optional api.shared.v1.PaginationRequest pagination = 4;
}

message GetVtuberUserSubscriptionByIdRequest {
  int64 id = 1; // @gotag: validate:"required"
}

message GetAllVtuberUserSubscriptionsOfUserRequest {
  optional int64 vtuber_id = 2;
  optional int64 vtuber_subscription_id = 3;
}

message GetAllVtuberUserSubscriptionsResponse {
  repeated VtuberUserSubscription vtubersubscription = 1;
  api.shared.v1.PaginationDetails pagination_details = 2;
}

message GetMyVtuberUserSubscriptionsRequest {
  optional int64 sub_id = 2;
  optional api.shared.v1.PaginationRequest pagination = 1;
}

message DeleteVtuberUserSubscriptionByIdRequest {
  int64 id = 1; // @gotag: validate:"required"
}

message UpdateVtuberUserSubscriptionRequest {
  bool is_recurring = 1;
  int64 id = 3; // @gotag: validate:"required"
}

service VtuberUserSubscriptionService {
  rpc AddVtuberUserSubscription(AddVtuberUserSubscriptionRequest) returns (AddVtuberUserSubscriptionResponse) {
    option (api.authz.v1.options) = {require: true};
  }
  rpc GetAllVtuberUserSubscriptions(GetAllVtuberUserSubscriptionsRequest) returns (GetAllVtuberUserSubscriptionsResponse) {
    option (api.authz.v1.options) = {
      require: true
      is_admin: true
    };
  }
  rpc GetVtuberUserSubscriptionById(GetVtuberUserSubscriptionByIdRequest) returns (VtuberUserSubscription);
  rpc DeleteVtuberUserSubscriptionById(DeleteVtuberUserSubscriptionByIdRequest) returns (api.shared.v1.GenericResponse) {
    option (api.authz.v1.options) = {require: true};
  }
  rpc UpdateVtuberUserSubscriptionById(UpdateVtuberUserSubscriptionRequest) returns (api.shared.v1.GenericResponse) {
    option (api.authz.v1.options) = {require: true};
  }
  rpc GetMyVtuberUserSubscriptions(GetMyVtuberUserSubscriptionsRequest) returns (GetAllVtuberUserSubscriptionsResponse) {
    option (api.authz.v1.options) = {require: true};
  }
}
