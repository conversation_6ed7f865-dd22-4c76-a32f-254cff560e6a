syntax = "proto3";

package api.campaigns.v1;

import "authz/v1/authz.proto";
import "campaigns/v1/campaignbanner.proto";
import "google/protobuf/timestamp.proto";
import "shared/v1/generic.proto";
import "shared/v1/pagination.proto";
import "shared/v1/profile.proto";
import "shared/v1/social_media_links.proto";
import "vtubers/v1/vtuberprofiles.proto";

option go_package = "github.com/nsp-inc/vtuber/api/campaigns/v1;campaignsv1";

message User {
  int64 id = 1;
  string name = 2;
  string image = 3;
  optional string comment = 4;
  google.protobuf.Timestamp created_at = 5;
}

message SubscriberResponse {
  repeated User data = 1;
  api.shared.v1.PaginationDetails pagination_details = 2;
}

message SubscriberRequest {
  int64 campaign_id = 1; // @gotag: validate:"required"
  optional api.shared.v1.PaginationRequest pagination = 2;
}

message GetMyCampaignsNameRequest {
}

message CampaignNames {
  string name = 1;
  int64 id = 2;
}
message GetMyCampaignNamesResponse {
  repeated CampaignNames data = 1;
}

message AddCampaignRequest {
  string name = 1; // @gotag: validate:"required"
  string description = 2; // @gotag: validate:"required"
  google.protobuf.Timestamp start_date = 3;
  google.protobuf.Timestamp end_date = 4;
  int32 total_budget = 5; // @gotag: validate:"required"
  repeated int64 categories = 6;
  string thumbnail = 7; // @gotag: validate:"required"
  string short_description = 10; // @gotag: validate:"required"
  string promotional_message = 11; // @gotag: validate:"required"
  api.shared.v1.SocialMediaLinks social_media_links = 12;
}

message AddCampaignResponse {
  Campaign data = 1;
}

message Campaign {
  int64 id = 1;
  string name = 2;
  string short_description = 3;
  google.protobuf.Timestamp start_date = 4;
  google.protobuf.Timestamp end_date = 5;
  int32 total_budget = 6;
  repeated int64 categories = 7;
  int64 vtuber_id = 8;
  google.protobuf.Timestamp created_at = 9;
  string thumbnail = 10;
  string promotional_message = 11;
  int32 total_raised = 12;
  api.shared.v1.SocialMediaLinks social_media_links = 13;
  string slug = 14;
  optional string created_by = 15;
}

message RelatedCampaignRequest {
  repeated int64 category_id = 1; // @gotag: validate:"required"
}

message RelatedCampaignResponse {
  repeated Campaign data = 1;
}

message GetAllCampaignsRequest {
  optional int64 category_id = 1;
  optional api.shared.v1.PaginationRequest pagination = 2;
}

message GetAllCampaignsByVtuberRequest {
  optional api.shared.v1.PaginationRequest pagination = 1;
  int64 vtuber_id = 2;
}

message GetMyCampaignsRequest {
  optional api.shared.v1.PaginationRequest pagination = 1;
}

message GetAllCampaignsResponse {
  repeated Campaign data = 1;
  api.shared.v1.PaginationDetails pagination_details = 2;
}

message GetCampaignByIdRequest {
  string id = 1; // @gotag: validate:"required"
}

message GetCampaignById {
  int64 id = 1;
  string name = 2;
  string description = 3;
  string short_description = 4;
  google.protobuf.Timestamp start_date = 5;
  google.protobuf.Timestamp end_date = 6;
  int32 total_budget = 7;
  repeated int64 categories = 8;
  api.vtubers.v1.VtuberProfile vtuber = 9;
  google.protobuf.Timestamp created_at = 10;
  string thumbnail = 11;
  repeated CampaignVariantById variants = 12;
  repeated api.campaigns.v1.CampaignBanner banners = 13;
  bool has_liked = 14;
  int64 like_count = 15;
  string promotional_message = 16;
  int32 total_raised = 17;
  api.shared.v1.SocialMediaLinks social_media_links = 18;
  string slug = 19;
}

message CampaignVariantById {
  int64 id = 1;
  int64 campaign_id = 2;
  int32 price = 3;
  string description = 4;
  int32 max_sub = 5;
  string image = 6;
  google.protobuf.Timestamp created_at = 7;
  int64 sub_count = 8;
  string title = 9;
  bool has_subscribed = 10;
}

message GetCampaignByIdResponse {
  GetCampaignById data = 1;
}

message DeleteCampaignByIdRequest {
  int64 id = 1; // @gotag: validate:"required"
}

message UpdateCampaignByIdRequest {
  string name = 1; // @gotag: validate:"required"
  string description = 2; // @gotag: validate:"required"
  google.protobuf.Timestamp start_date = 3;
  google.protobuf.Timestamp end_date = 4;
  int64 id = 5; // @gotag: validate:"required"
  int32 total_budget = 6; // @gotag: validate:"required"
  repeated int64 categories = 7;
  string thumbnail = 8;
  string short_description = 9; // @gotag: validate:"required";
  string promotional_message = 10; // @gotag: validate:"required"
  api.shared.v1.SocialMediaLinks social_media_links = 11;
}

message CampaignSubscriptionComments {
  int64 id = 1;
  api.shared.v1.Profile user = 2;
  google.protobuf.Timestamp created_at = 3;
  string comment = 4;
}

message GetCampaignSubscriptionCommentsRequest {
  int64 campaign_id = 1;
  optional api.shared.v1.PaginationRequest pagination = 2;
}

message GetCampaignSubscriptionCommentsReponse {
  repeated CampaignSubscriptionComments data = 1;
  api.shared.v1.PaginationDetails pagination_details = 2;
}

message GetSupportedCampaignRequest {
  optional api.shared.v1.PaginationRequest pagination = 1;
}

message PopularCampaignRequest {}

message DeleteCampaignByIdResponse {
  string message = 1;
  bool success = 2;
}

message UpdateCampaignByIdResponse {
  string message = 1;
  bool success = 2;
}

service CampaignService {
  rpc AddCampaign(AddCampaignRequest) returns (AddCampaignResponse) {
    option (api.authz.v1.options) = {
      require: true
      expression: "_user.vtuberId!=null"
      is_vtuber: true
    };
  }
  rpc GetSubscribers(SubscriberRequest) returns (SubscriberResponse);
  rpc GetAllCampaigns(GetAllCampaignsRequest) returns (GetAllCampaignsResponse);
  rpc GetAllCampaignsByVtuberId(GetAllCampaignsByVtuberRequest) returns (GetAllCampaignsResponse);
  rpc GetMyCampaigns(GetMyCampaignsRequest) returns (GetAllCampaignsResponse) {
    option (api.authz.v1.options) = {
      require: true
      expression: "_user.vtuberId!=null"
    };
  }
  rpc GetCampaignById(GetCampaignByIdRequest) returns (GetCampaignByIdResponse) {}
  rpc DeleteCampaignById(DeleteCampaignByIdRequest) returns (DeleteCampaignByIdResponse) {
    option (api.authz.v1.options) = {
      require: true
      expression: "_user.vtuberId!=null"
    };
  }
  rpc UpdateCampaignById(UpdateCampaignByIdRequest) returns (UpdateCampaignByIdResponse) {
    option (api.authz.v1.options) = {
      require: true
      expression: "_user.vtuberId!=null"
    };
  }
  rpc GetCampaignSubscriberComments(GetCampaignSubscriptionCommentsRequest) returns (GetCampaignSubscriptionCommentsReponse) {}
  rpc GetMySupportedCampaigns(GetSupportedCampaignRequest) returns (GetAllCampaignsResponse) {
    option (api.authz.v1.options) = {require: true};
  }
  rpc GetPopularCampaign(PopularCampaignRequest) returns (Campaign) {}
  rpc GetRelatedCampaign(RelatedCampaignRequest) returns (RelatedCampaignResponse) {}
  rpc GetMyCampaignNames(GetMyCampaignsNameRequest) returns (GetMyCampaignNamesResponse) {}
}
