syntax = "proto3";

package api.campaigns.v1;

import "authz/v1/authz.proto";
import "google/protobuf/timestamp.proto";
import "shared/v1/generic.proto";
import "shared/v1/pagination.proto";

option go_package = "github.com/nsp-inc/vtuber/api/campaigns/v1;campaignsv1";

message AddCampaignVariantRequest {
  int64 campaign_id = 1; // @gotag: validate:"required"
  int32 price = 2; // @gotag: validate:"gte=1"
  string description = 3; // @gotag: validate:"required"
  int32 max_sub = 4; // @gotag: validate:"gte=0"
  string image = 5; // @gotag: validate:"required"
  string title = 6; // @gotag: validate:"required"
}

message GetCampaignSubResponse {
  repeated SubDetails subs = 1;
  optional api.shared.v1.PaginationDetails pagination = 2;
}

message SubDetails {
  int64 id = 1;
  string name = 2;
  optional string image = 3;
  int32 amount = 4;
}

message GetCampaignSubRequest {
  int64 id = 1; // @gotag: validate:"required"
  optional api.shared.v1.PaginationRequest pagination = 2;
}

message AddCampaignVariantResponse {
  CampaignVariant data = 1;
}

message CampaignVariant {
  int64 id = 1;
  int64 campaign_id = 2;
  int32 price = 3;
  string description = 4;
  int32 max_sub = 5;
  string image = 6;
  google.protobuf.Timestamp created_at = 7;
  int64 sub_count = 8;
  string title = 9;
}

message GetAllCampaignVariantsRequest {
  int64 campaign_id = 1; // @gotag: validate:"required"
}

message GetAllCampaignVariantsResponse {
  repeated CampaignVariant data = 1;
}

message GetCampaignVariantByIdRequest {
  int64 id = 1; // @gotag: validate:"required"
}

message GetCampaignVariantByIdResponse {
  CampaignVariant data = 1;
}

message DeleteCampaignVariantByIdRequest {
  int64 id = 1; // @gotag: validate:"required"
}

message UpdateCampaignVariantByIdRequest {
  int64 id = 1; // @gotag: validate:"required"
  int32 price = 3; // @gotag: validate:"required"
  string description = 4; // @gotag: validate:"required"
  int32 max_sub = 5; // @gotag: validate:"required"
  string image = 6; // @gotag: validate:"required"
  string title = 7; // @gotag: validate:"required"
}

message GetCampaignVariantSubsByIdRequest {
  int64 id = 1; // @gotag: validate:"required"
}

message DeleteCampaignVariantByIdResponse {
  bool success = 1;
  string message = 2;
}

message UpdateCampaignVariantByIdResponse {
  bool success = 1;
  string message = 2;
}

service CampaignVariantService {
  rpc AddCampaignVariant(AddCampaignVariantRequest) returns (AddCampaignVariantResponse) {
    option (api.authz.v1.options) = {
      require: true
      is_vtuber: true
      expression: "_user.vtuberId!=null"
    };
  }
  rpc GetAllCampaignVariants(GetAllCampaignVariantsRequest) returns (GetAllCampaignVariantsResponse) {}
  rpc GetCampaignVariantById(GetCampaignVariantByIdRequest) returns (GetCampaignVariantByIdResponse) {}
  rpc DeleteCampaignVariantById(DeleteCampaignVariantByIdRequest) returns (DeleteCampaignVariantByIdResponse) {
    option (api.authz.v1.options) = {
      require: true
      is_vtuber: true
      expression: "_user.vtuberId!=null"
    };
  }
  rpc UpdateCampaignVariantById(UpdateCampaignVariantByIdRequest) returns (UpdateCampaignVariantByIdResponse) {
    option (api.authz.v1.options) = {
      require: true
      is_vtuber: true
      expression: "_user.vtuberId!=null"
    };
  }
  rpc GetCampaignVariantSubs(GetCampaignSubRequest) returns (GetCampaignSubResponse) {
    option (api.authz.v1.options) = {require: true};
  }
}
