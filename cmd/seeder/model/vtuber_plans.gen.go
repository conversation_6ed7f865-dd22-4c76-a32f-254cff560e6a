package model

import (
	"time"

	"gorm.io/gorm"
)

const TableNameVtuberPlan = "vtuber_plans"

// VtuberPlan mapped from table <vtuber_plans>
type VtuberPlan struct {
	ID               int64          `gorm:"column:id;primaryKey;autoIncrement:true" json:"id"`
	VtuberID         int64          `gorm:"column:vtuber_id;not null" json:"vtuber_id"`
	Description      string         `gorm:"column:description;not null" json:"description"`
	Title            string         `gorm:"column:title;not null" json:"title"`
	ShortDescription string         `gorm:"column:short_description;not null" json:"short_description"`
	Price            int32          `gorm:"column:price;not null" json:"price"`
	Index            int32          `gorm:"column:index;not null" json:"index"`
	CreatedAt        time.Time      `gorm:"column:created_at;not null;default:CURRENT_TIMESTAMP" json:"created_at"`
	UpdatedAt        time.Time      `gorm:"column:updated_at;not null;default:CURRENT_TIMESTAMP" json:"updated_at"`
	DeletedAt        gorm.DeletedAt `gorm:"column:deleted_at" json:"deleted_at"`
	AnnualPrice      int32          `gorm:"column:annual_price;not null" json:"annual_price"`
}

// TableName VtuberPlan's table name
func (*VtuberPlan) TableName() string {
	return TableNameVtuberPlan
}
