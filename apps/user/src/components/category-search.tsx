import { useQuery } from "@tanstack/react-query";
import { useNavigate } from "@tanstack/react-router";
import { useCategories } from "@vtuber/auth/hooks";
import { useLanguage } from "@vtuber/language/hooks";
import { vtuberCategoryClient } from "@vtuber/services/client";
import { SearchButton } from "@vtuber/ui/components/search-button";
import { DottedShape } from "@vtuber/ui/components/shape/dotted";
import { cn } from "@vtuber/ui/lib/utils";
import { Search } from "lucide-react";
import { motion } from "motion/react";

export const CategorySearch = ({ className }: { className?: string }) => {
  const { data } = useQuery({
    queryKey: ["vtuber_categories"],
    queryFn: async () => {
      const [data] = await vtuberCategoryClient.getAllVtuberCategories({});
      return data;
    },
  });
  const { categories, getMultipleCategories } = useCategories();
  const navigate = useNavigate();

  const vtuberCategories = getMultipleCategories(
    data?.data?.map((c) => c.id) || [],
  );

  const categoriesList =
    categories.length > 10 ? categories.slice(0, 10) : categories;

  const vtuberCategoriesList =
    vtuberCategories.length > 10
      ? vtuberCategories.slice(0, 10)
      : vtuberCategories;

  const { getText, language } = useLanguage();
  return (
    <div
      className={cn(
        "relative bg-[#2C2A37] rounded-sm md:py-[72px] py-16 md:px-[51px] px-5 flex flex-col gap-y-12",
        className,
      )}>
      <DottedShape className="absolute -top-12 sm:-left-12 -left-5 -z-10" />
      <div className="grid grid-cols-12 justify-between md:items-center md:gap-y-0 gap-y-[35px]">
        <section className="flex md:col-span-3 col-span-12 items-start gap-x-3">
          <div className="bg-[#F0940E] h-[2px] w-4 mt-4" />
          {language === "ja" ? (
            <h3 className="text-lg font-bold text-[#F7F5F5] whitespace-nowrap">
              イベント/クラファン <br className="sm:block hidden" /> から探す
            </h3>
          ) : (
            <h3 className="text-lg font-bold text-[#F7F5F5] whitespace-nowrap">
              Search by event / <br className="sm:block hidden" />
              crowdfunding
            </h3>
          )}
        </section>
        <section className="flex md:col-span-7 col-span-12 items-center gap-4 flex-wrap">
          {categoriesList.filter(Boolean).map((c) => (
            <button
              onClick={() => {
                navigate({
                  to: "/search",
                  search: {
                    query: c.id.toString(),
                  },
                });
              }}
              className="rounded-xs border border-white px-6 py-[5px] hover:bg-white/10 text-sm font-medium"
              key={c.id}>
              {c.name}
            </button>
          ))}
        </section>
        <section className="flex md:col-span-2 col-span-12 justify-end">
          <SearchButton
            variant={"underlined"}
            className="h-14">
            {getText("see_list")} <Search className="ml-8" />
          </SearchButton>
        </section>
      </div>
      <div className="grid grid-cols-12 justify-between md:items-center md:gap-y-0 gap-y-[35px]">
        <section className="flex md:col-span-3 col-span-12 items-center gap-x-3">
          <div className="bg-[#F0940E] h-[2px] w-4" />
          <h3 className="text-lg font-bold text-[#F7F5F5] whitespace-nowrap">
            {getText("search_by_vtuber")}
          </h3>
        </section>
        <section className="flex md:col-span-7 col-span-12 items-center gap-4 flex-wrap">
          {vtuberCategoriesList?.filter(Boolean).map(
            (c) =>
              !!c?.name && (
                <button
                  onClick={() => {
                    navigate({
                      to: "/search",
                      search: {
                        query: c?.id.toString(),
                      },
                    });
                  }}
                  className="rounded-xs border border-white px-6 py-[5px] hover:bg-white/10 text-sm font-medium"
                  key={c?.id}>
                  {c?.name}
                </button>
              ),
          )}
        </section>
        <section className="flex md:col-span-2 col-span-12 justify-end">
          <SearchButton
            variant={"underlined"}
            className="h-14">
            {getText("see_list")} <Search className="ml-8" />
          </SearchButton>
        </section>
      </div>

      <motion.div
        animate={{
          y: [0, 10, 0],
          transition: {
            duration: 2.5,
            repeat: Infinity,
            ease: "easeInOut",
          },
        }}
        className="flex items-center absolute -bottom-10 -z-10 sm:-right-10 -right-5">
        <DottedShape />
        <DottedShape />
      </motion.div>
    </div>
  );
};
