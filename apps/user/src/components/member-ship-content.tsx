import { useLanguage } from "@vtuber/language/hooks";
import { VtuberPlan } from "@vtuber/services/vtubers";
import { Button } from "@vtuber/ui/components/button";
import { Tabs, TabsList, TabsTrigger } from "@vtuber/ui/components/tabs";
import { cn } from "@vtuber/ui/lib/utils";
import { useState } from "react";
import { MembershipItem } from "./membership-item";

interface Props {
  data?: VtuberPlan[];
  isPending: boolean;
  refetch?: () => void;
  className?: string;
  conditionClasses?: string;
}

export const MembershipContent = ({
  isPending,
  data,
  refetch,
  className,
  conditionClasses,
}: Props) => {
  const [tabValue, setTabValue] = useState<"annual" | "monthly">("monthly");
  const { getText, language } = useLanguage();
  if (isPending)
    return (
      <div
        className={cn(
          "w-full flex items-center justify-center text-2xl font-semibold min-h-[844px]",
          conditionClasses,
        )}>
        {getText("loading")}...
      </div>
    );

  if (!data || data.length === 0)
    return (
      <div
        className={cn(
          "w-full flex items-center justify-center text-2xl font-semibold min-h-[844px]",
          conditionClasses,
        )}>
        <div className="text-center space-y-1">
          <p>{getText("something_went_wrong")}</p>
          <p>{getText("please_try_again")}</p>
          <div className="pt-6">
            <Button
              size={"lg"}
              onClick={refetch}
              variant={"outline-dark"}>
              {getText("try_again")}
            </Button>
          </div>
        </div>
      </div>
    );

  return (
    <div className={cn("grid sm:gap-y-14 gap-y-8", className)}>
      <h3 className="sm:text-4xl text-2xl font-bold text-font text-center">
        {getText("paid_membership")}
      </h3>
      <Tabs
        defaultValue="annual"
        variant={"subtle"}
        value={tabValue}
        onValueChange={(v) => setTabValue(v as "annual" | "monthly")}>
        <div className="flex justify-center">
          <TabsList className="justify-center items-stretch gap-0 md:w-fit sm:w-[50%] w-full rounded-[5px] overflow-hidden">
            <TabsTrigger
              value="monthly"
              className="w-full p-0 py-1 md:w-[226px] text-sm ">
              {getText("monthly_plan")}
            </TabsTrigger>
            <TabsTrigger
              value="annual"
              className="w-full p-0 py-1 md:w-[226px] text-sm">
              {language === "ja" ? (
                <div>
                  年間プラン <br /> (月払い)
                </div>
              ) : (
                <div>
                  Annual Plan <br /> (Paid monthly)
                </div>
              )}
            </TabsTrigger>
          </TabsList>
        </div>
        <div className="grid lg:grid-cols-3 grid-cols-1 items-stretch md:gap-6 gap-8 sm:pt-14 pt-8">
          {data.map((d, i) => (
            <MembershipItem
              key={d.id}
              data={d}
              tabValue={tabValue}
              index={i}
            />
          ))}
        </div>
      </Tabs>
    </div>
  );
};
