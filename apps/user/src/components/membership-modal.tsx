import { useQuery } from "@connectrpc/connect-query";
import { VtuberPlanService } from "@vtuber/services/vtubers";
import { ButtonProps } from "@vtuber/ui/components/button";
import {
  Dialog,
  DialogContent,
  DialogTrigger,
} from "@vtuber/ui/components/dialog";
import { CloseIcon } from "@vtuber/ui/components/icons/close-icon";
import { ScrollArea, ScrollBar } from "@vtuber/ui/components/scroll-area";
import { useState } from "react";
import { MembershipContent } from "./member-ship-content";

type Props = ButtonProps & {
  vtuberId: bigint;
};

export const MembershipModal = ({ vtuberId, ...props }: Props) => {
  const [opened, setOpened] = useState(false);

  const { data, isPending, refetch, isRefetching } = useQuery(
    VtuberPlanService.method.getAllVtuberPlansByVtuberId,
    {
      vtuberId,
    },
    {
      enabled: opened,
    },
  );
  return (
    <Dialog
      open={opened}
      onOpenChange={setOpened}>
      <DialogTrigger {...props} />
      <DialogContent
        withCloseButton={false}
        className="space-y-10 bg-transparent h-full w-full max-w-full p-0 border-none">
        <button
          className="absolute top-4 right-4"
          onClick={() => setOpened(false)}>
          <CloseIcon className="md:w-[47.5px] md:h-[39px] h-[22px] w-[26.5px]" />
        </button>
        <div className="md:w-[70%] w-[90%] mx-auto md:pt-0 pt-6">
          <ScrollArea className="flex flex-col max-h-[844px] overflow-y-auto w-full md:px-[104px] md:rounded-[18px] md:bg-gradient-6">
            <MembershipContent
              className="md:py-14 md:pb-14 pb-56"
              isPending={isPending || isRefetching}
              data={data?.VtuberPlan}
              refetch={refetch}
            />
            <ScrollBar orientation="horizontal" />
          </ScrollArea>
        </div>
      </DialogContent>
    </Dialog>
  );
};
