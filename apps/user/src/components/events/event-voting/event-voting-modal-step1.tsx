import { Link } from "@tanstack/react-router";
import { Avatar } from "@vtuber/ui/components/avatar";
import { Button } from "@vtuber/ui/components/button";
import { ExternalLinks } from "@vtuber/ui/components/external-links";
import { X } from "lucide-react";
import { useEventVotingModal } from "./event-voting-modal-provider";

export const EventVotingStep1 = () => {
  const { onClose, eventParticipant, onNextStep } = useEventVotingModal();
  return (
    <div>
      <header className="flex justify-end px-8 py-6 border-b-4 border-b-[#302e41]">
        <button onClick={onClose}>
          <X />
        </button>
      </header>
      <div className="px-8 py-6 grid gap-y-6">
        <section className="flex flex-col items-center gap-y-5">
          <Avatar
            className="size-[156px]"
            src={eventParticipant?.vtuber?.image}
            fallback={eventParticipant?.vtuber?.name || ""}
          />
          <p className="text-font text-[22px] font-medium">
            {eventParticipant?.vtuber?.name}
          </p>
        </section>
        <section className="grid gap-y-2">
          <p className="text-sm text-font">
            {eventParticipant?.vtuber?.introduction}
          </p>
          <Link
            to="/vtuber/$id"
            params={{
              id: eventParticipant?.vtuber?.id.toString() || "",
            }}
            className="text-tertiary font-medium text-sm">
            Vtuberのプロフィールを見る
          </Link>
        </section>
        <ExternalLinks
          socialMediaLinks={eventParticipant?.vtuber?.socialMediaLinks}
        />
        <Button
          variant={"tertiary"}
          size={"xl"}
          className="text-white font-bold rounded-xs"
          onClick={onNextStep}>
          投票して応援する
        </Button>
      </div>
    </div>
  );
};
