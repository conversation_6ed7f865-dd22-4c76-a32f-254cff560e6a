import { useQuery } from "@connectrpc/connect-query";
import { useNavigate, useSearch } from "@tanstack/react-router";
import { CampaignService, GetCampaignById } from "@vtuber/services/campaigns";
import {
  Tabs,
  TabsContent,
  Ta<PERSON>List,
  TabsTrigger,
} from "@vtuber/ui/components/tabs";
import { useScrollInToView } from "@vtuber/ui/hooks/use-scroll-intoview";
import { useEffect, useState } from "react";

import { timestampDate } from "@bufbuild/protobuf/wkt";
import { useLanguage } from "@vtuber/language/hooks";
import { Post } from "@vtuber/services/content";
import { Avatar } from "@vtuber/ui/components/avatar";
import { DisplayTag } from "@vtuber/ui/components/display-tag";
import { MarkDown } from "@vtuber/ui/components/markdown";
import { Separator } from "@vtuber/ui/components/separator";
import { LoadMoreButton } from "../load-more-button";
import { PostCard } from "../post/post-card";

type Props = {
  campaign: GetCampaignById;
  posts: Post[];
};

export const CampaignOverviewTabs = ({ campaign, posts }: Props) => {
  const { getText } = useLanguage();
  const { commentId, tab } = useSearch({ from: "/_app/campaign/$id" });
  const [tabValue, setTabValue] = useState(tab || "introduction");
  const navigate = useNavigate();
  const { ref } = useScrollInToView({ trigger: commentId });

  useEffect(() => {
    if (tab) {
      setTabValue(tab);
    }
  }, [tab]);

  const { data: subscribers, isPending: loadingSubscribers } = useQuery(
    CampaignService.method.getCampaignSubscriberComments,
    {
      campaignId: BigInt(campaign?.id || ""),
    },
    {
      enabled: tabValue === "supporters" && !!campaign?.id,
    },
  );

  const hasSubscribed = campaign.variants.some((v) => v.hasSubscribed);

  return (
    <div ref={ref}>
      <Tabs
        variant={"outline"}
        value={tabValue}
        onValueChange={(val) => {
          setTabValue(val);
          navigate({
            // @ts-ignore
            search: (prev) => ({
              ...prev,
              tab: val,
            }),
            resetScroll: false,
          });
        }}>
        <TabsList className="md:justify-center justify-start w-full md:flex-nowrap flex-wrap gap-2">
          <TabsTrigger
            value="introduction"
            className="sm:text-xl text-base sm:min-w-max flex-1">
            {getText("project_introduction")}
          </TabsTrigger>
          <TabsTrigger
            value="activity"
            className="sm:text-xl text-base sm:min-w-max flex-1">
            {getText("activity_report")}
          </TabsTrigger>
          <TabsTrigger
            value="supporters"
            className="sm:text-xl text-base sm:min-w-max flex-1">
            {getText("supporters")}
          </TabsTrigger>
        </TabsList>
        <TabsContent value="introduction">
          <article className="pt-10 grid gap-y-10">
            <DisplayTag text="project_introduction" />
            <MarkDown
              markdown={campaign?.description}
              className="text-font"
            />
          </article>
        </TabsContent>
        <TabsContent value="activity">
          <div className="pt-10 grid gap-y-10">
            <DisplayTag text="activity_report" />
            <section className="grid gap-y-10">
              {posts.length > 0 ? (
                posts.map((p) => (
                  <PostCard
                    vtuberId={p.vtuber?.id!}
                    shouldBlur={p.membershipOnly && !hasSubscribed}
                    key={p.id}
                    post={p}
                  />
                ))
              ) : (
                <div className="text-center py-12 bg-sub rounded-lg">
                  <p className="text-muted-foreground">No Posts Available</p>
                </div>
              )}
            </section>
          </div>
        </TabsContent>
        <TabsContent value="supporters">
          <div className="pt-10 grid gap-y-10">
            <DisplayTag text="supporters" />
            {loadingSubscribers ? (
              <div className="flex items-center justify-center h-40">
                {getText("loading")}...
              </div>
            ) : !subscribers || subscribers.data.length === 0 ? (
              <div>
                <div className="text-center py-12 bg-sub rounded-lg">
                  <p className="text-muted-foreground">
                    {getText("No_Subscribers_Yet")}
                  </p>
                </div>
              </div>
            ) : (
              <div className="grid gap-y-10">
                {subscribers.data.map((s) => (
                  <div
                    key={s.id}
                    className="text-font grid gap-y-6">
                    <div className="grid gap-y-4">
                      <div className="flex sm:items-center items-start sm:justify-between sm:flex-row flex-col sm:gap-y-0 gap-y-[11px]">
                        <div className="flex items-center gap-x-3">
                          <Avatar
                            className="size-[45px]"
                            fallback={s.user?.name}
                            alt={s.user?.name}
                            src={s.user?.image}
                          />
                          <p className="font-medium">{s.user?.name}</p>
                        </div>
                        <p className="text-sm font-medium">
                          {timestampDate(s.createdAt!).toLocaleDateString()}
                        </p>
                      </div>
                      <p>{s.comment}</p>
                    </div>
                    <Separator />
                  </div>
                ))}
              </div>
            )}

            <LoadMoreButton
              itemsLength={subscribers?.data?.length || 0}
              totalItems={subscribers?.paginationDetails?.totalItems || 0}
            />
          </div>
        </TabsContent>
        {/* <TabsContent value="comment">
          {isPending ? (
            <div className="flex items-center justify-center h-40">
              {getText("loading")}...
            </div>
          ) : comments && comments.length > 0 ? (
            <ScrollArea
              className="flex max-h-[600px] flex-col overflow-y-auto"
              ref={commentRef}
              id="test">
              <div className="space-y-10 px-3">
                {comments.map((c) => {
                  const canDelete =
                    user?.vtuber?.id === campaign?.vtuber?.id ||
                    c.user?.id === user?.id;
                  return (
                    <div
                      id={`campaign-comment-${c.id}`}
                      key={c.id}
                      className="transition-colors duration-300">
                      <CampaignComments
                        refetch={refetch}
                        comment={c}
                        canDelete={canDelete}
                      />
                    </div>
                  );
                })}
              </div>
            </ScrollArea>
          ) : (
            <p className="text-center">{getText("no_comments_yet")}</p>
          )}

          <CampaignCommentForm
            refetch={refetch}
            campaignId={campaign?.id!}
          />
        </TabsContent> */}
      </Tabs>
    </div>
  );
};
