import { timestampDate } from "@bufbuild/protobuf/wkt";
import { Link } from "@tanstack/react-router";
import { useCategories } from "@vtuber/auth/hooks";
import { useLanguage } from "@vtuber/language/hooks";
import { Campaign } from "@vtuber/services/campaigns";
import { AspectRatio } from "@vtuber/ui/components/aspect-ratio";
import { Card, CardContent } from "@vtuber/ui/components/card";
import { Image } from "@vtuber/ui/components/image";
import { MarkDown } from "@vtuber/ui/components/markdown";
import { Progress } from "@vtuber/ui/components/progress";
import { RemainingDays } from "@vtuber/ui/components/remaining-days";
import { Tag } from "@vtuber/ui/components/tag";
import { cn, getProgress } from "@vtuber/ui/lib/utils";

interface Props {
  campaign: Campaign;
  className?: string;
}
export const CampaignCard = ({ campaign, className }: Props) => {
  const { getMultipleCategories } = useCategories();
  const { getText } = useLanguage();
  const progress = getProgress(campaign.totalBudget, campaign.totalRaised);
  const tags = getMultipleCategories(campaign.categories);

  return (
    <Link
      to="/campaign/$id"
      params={{
        id: campaign.slug,
      }}>
      <Card className="bg-transparent border-none shadow-none flex flex-col gap-y-2">
        <AspectRatio ratio={571 / 321}>
          <Image
            className={cn(
              "rounded-10 border border-white object-cover",
              className,
            )}
            src={campaign.thumbnail}
            alt={campaign.name}
          />
        </AspectRatio>
        <div className="flex sm:items-center items-start sm:flex-row flex-col sm:gap-x-6 sm:gap-y-0 gap-y-[11px]">
          <p className="text-[#F7F5F5] sm:text-sm text-xs sm:font-bold font-normal">
            {timestampDate(campaign.startDate!).toLocaleDateString()} -{" "}
            {timestampDate(campaign.endDate!).toLocaleDateString()}{" "}
          </p>
          {/* <Tag
            className="text-white py-1 sm:px-3 px-2 sm:text-sm text-xs font-medium"
            variant={"success"}>
           {campaign.s}
          </Tag> */}
        </div>
        <CardContent className="p-0 space-y-[14px]">
          <section>
            <div className="flex sm:items-center sm:flex-row flex-col items-start sm:justify-between justify-start w-full">
              <h3 className="font-bold sm:text-[33px] text-[23px] text-font">
                {campaign.totalBudget.toLocaleString()}{" "}
                <span className="text-[22px]">{getText("yen")}</span>
              </h3>
              <RemainingDays
                className="sm:text-[17px] text-[10px] font-medium"
                daysClassname="sm:text-[34px] text-[22px] font-bold"
                endDate={campaign.endDate!}
                startDate={campaign.startDate!}
              />
            </div>
            <Progress
              value={progress}
              className="h-[10px]"
            />
          </section>
          <MarkDown
            markdown={campaign.shortDescription}
            className="sm:text-lg text-sm font-bold text-font"
          />
          {tags?.length > 0 && (
            <div className="flex items-center gap-3 flex-wrap">
              {tags.filter(Boolean).map((c) => (
                <Tag
                  key={c?.id}
                  variant={"outline-white"}
                  className="p-3 w-max">
                  {c?.name}
                </Tag>
              ))}
            </div>
          )}
        </CardContent>
      </Card>
    </Link>
  );
};
