import { timestampDate } from "@bufbuild/protobuf/wkt";
import { useRouter } from "@tanstack/react-router";
import { useAuth } from "@vtuber/auth/hooks";
import { Post } from "@vtuber/services/content";
import { AspectRatio } from "@vtuber/ui/components/aspect-ratio";
import { Card, CardContent, CardHeader } from "@vtuber/ui/components/card";
import { ExpandableText } from "@vtuber/ui/components/expandable-text";
import { Media } from "@vtuber/ui/components/media";
import { Spinner } from "@vtuber/ui/components/spinner";
import useIntersectionObserver from "@vtuber/ui/hooks/use-intersection-observer";
import { useIsMobile } from "@vtuber/ui/hooks/use-mobile";
import { cn } from "@vtuber/ui/lib/utils";
import { format } from "date-fns";
import { Heart, MessageCircle } from "lucide-react";
import { useMemo, useState } from "react";
import { PostCommentsModal } from "../campaign/post-comments-modal";
import { MembershipModal } from "../membership-modal";
import { LikePost } from "./like-post";
import { PostCardMemberOnlyOverlay } from "./post-card-member-only-overlay";
import { PostCardNotLoggedInOverlay } from "./post-card-not-logged-in-overlay";

interface Props {
  className?: string;
  post: Post;
  variant?: "default" | "gradient";
  vtuberId: bigint;
  shouldBlur?: boolean;
}

export const PostCard = ({
  className,
  post,
  variant,
  shouldBlur,
  vtuberId,
}: Props) => {
  const { isMobile } = useIsMobile();
  const router = useRouter();
  const [opened, setOpened] = useState(false);
  const { session } = useAuth();
  const { ref, isIntersecting } = useIntersectionObserver({
    freezeOnceVisible: true,
  });

  const formatedDate = (date: Date) =>
    useMemo(() => {
      return format(date, "yyyy/MM/dd HH:mm");
    }, [date]);

  const onClick = () => {
    if (!session) {
      router.navigate({
        to: "/login",
        search: {
          redirect: location.pathname,
        },
      });
      return;
    }
    if (isMobile) {
      router.navigate({
        to: "/vtuber/post/$id",
        params: {
          id: post.id.toString(),
        },
      });
      return;
    }
    setOpened(true);
  };

  return (
    <Card
      className={cn(
        "border-none shadow-none md:px-[39px] md:py-[42px] py-6 px-5 rounded-sm",
        variant === "gradient" ? "bg-gradient-3" : "bg-dark",
        className,
      )}
      ref={ref}>
      <CardHeader
        className={cn(
          "p-0 rounded-xl overflow-hidden",
          isIntersecting && "animate-zoom-in duration-700",
        )}>
        {post.media &&
          (session && shouldBlur ? (
            <MembershipModal vtuberId={vtuberId}>
              <AspectRatio ratio={574 / 309}>
                <Media
                  canEnableFullScreen={false}
                  style={{
                    viewTransitionName: `post-${post.id}`,
                  }}
                  onClick={onClick}
                  type={post?.mediaType}
                  src={post.media || ""}
                  alt={post.name}
                  showControls={false}
                  className="w-full h-full object-cover rounded-sm aspect-auto"
                />
              </AspectRatio>
            </MembershipModal>
          ) : (
            <PostCommentsModal
              slug={post.slug}
              postId={post.id}
              opened={opened}>
              <AspectRatio ratio={574 / 309}>
                <Media
                  canEnableFullScreen={false}
                  style={{
                    viewTransitionName: `post-${post.id}`,
                  }}
                  onClick={onClick}
                  type={post?.mediaType}
                  src={post.media || ""}
                  alt={post.name}
                  showControls={false}
                  className="w-full h-full object-cover rounded-sm aspect-auto"
                />
              </AspectRatio>
            </PostCommentsModal>
          ))}
      </CardHeader>
      <CardContent className="p-0 pt-6 space-y-5 relative">
        <h3 className="sm:text-2xl text-lg font-bold">{post.title}</h3>
        <p className="sm:text-sm text-xs font-medium text-font">
          {formatedDate(timestampDate(post.createdAt!))}
        </p>
        {!session && <PostCardNotLoggedInOverlay />}
        {session && shouldBlur && (
          <PostCardMemberOnlyOverlay vtuberId={vtuberId} />
        )}
        <ExpandableText
          className="sm:text-base text-sm text-font block"
          wrapperClassName="space-y-3"
          length={250}
          text={post.shortDescription}
        />
        <div className="flex items-center gap-3">
          <LikePost
            postId={post.id}
            hasLiked={post.hasLiked}
            className="p-0 hover:bg-transparent"
            postLikes={post.postLikes}>
            {({ postLikes, isLoading, isLiked, justLiked }) => (
              <div className="flex items-center gap-x-3">
                {isLoading ? (
                  <Spinner />
                ) : (
                  <Heart
                    className={cn(
                      "h-[21px] w-[22px]",
                      isLiked ? "fill-red-600 text-red-600" : "text-font",
                      {
                        "animate-zoom-in": justLiked,
                      },
                    )}
                  />
                )}
                <small className="text-sm font-medium text-font">
                  {postLikes}
                </small>
              </div>
            )}
          </LikePost>
          <PostCommentsModal
            slug={post.slug}
            postId={post.id}
            className="flex items-center gap-x-3">
            <button onClick={onClick}>
              <MessageCircle className="scale-[-1] rotate-90 text-font h-[21px] w-[22px]" />
            </button>
            <small className="text-sm font-medium text-font">
              {post.postComments}
            </small>
          </PostCommentsModal>
        </div>
      </CardContent>
    </Card>
  );
};
