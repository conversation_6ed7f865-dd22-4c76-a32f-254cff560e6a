import { timestampDate } from "@bufbuild/protobuf/wkt";
import { useSuspenseQuery } from "@tanstack/react-query";
import { useLocation, useNavigate } from "@tanstack/react-router";
import { useAuth } from "@vtuber/auth/hooks";
import { useLanguage } from "@vtuber/language/hooks";
import { Post } from "@vtuber/services/content";
import { VtuberProfile } from "@vtuber/services/vtubers";
import { CardContent } from "@vtuber/ui/components/card";
import { Media } from "@vtuber/ui/components/media";
import { toast } from "sonner";
import { recommendedPostsQueryOptions } from "~/utils/api";
import { MembershipModal } from "../membership-modal";

interface Props {
  // vtuberId: string;
  vtuber: VtuberProfile;
}

export const RecommendedPosts = ({ vtuber }: Props) => {
  const { getText } = useLanguage();
  const { data } = useSuspenseQuery(
    recommendedPostsQueryOptions({
      size: 5,
      // vtuberId,
    }),
  );

  const posts = data?.data;

  if (!posts || posts.length === 0)
    return (
      <CardContent className="h-40 p-0 flex items-center justify-center">
        {getText("no_recommended_posts")}
      </CardContent>
    );
  return (
    <CardContent className="p-0 grid gap-y-5">
      {posts.map((p) => (
        <RecommendedPostWrapper
          post={p}
          vtuber={vtuber}
          key={p.id}>
          <div className="grid grid-cols-12 gap-4 items-center">
            <Media
              src={p.media}
              isPreview
              showControls={false}
              type={p.mediaType}
              className="col-span-4 h-[57px] rounded-[5px] w-full aspect-auto"
            />
            <div className="col-span-8 space-y-1 text-left">
              <p className="text-sm text-font">{p.title}</p>
              <p className="text-xs text-[#707070]">
                {timestampDate(p.createdAt!).toLocaleDateString()}
              </p>
            </div>
          </div>
        </RecommendedPostWrapper>
      ))}
    </CardContent>
  );
};

const RecommendedPostWrapper = ({
  post,
  vtuber,
  children,
}: {
  post: Post;
  vtuber: VtuberProfile;
  children: React.ReactNode;
}) => {
  const { session } = useAuth();
  const navigate = useNavigate();
  const { pathname } = useLocation();
  const { getText } = useLanguage();

  if (session && !vtuber.isUserSubscribed && post.membershipOnly) {
    return <MembershipModal vtuberId={vtuber.id}>{children}</MembershipModal>;
  }

  return (
    <button
      onClick={() => {
        if (!session) {
          navigate({
            to: "/login",
            search: {
              redirect: pathname,
            },
          });
          toast.info(getText("login_to_see_post"));
          return;
        }
        navigate({
          to: "/vtuber/post/$id",
          params: {
            id: post.slug,
          },
        });
      }}>
      {children}
    </button>
  );
};
