import { useLanguage } from "@vtuber/language/hooks";
import { Button } from "@vtuber/ui/components/button";
import { LockIcon } from "@vtuber/ui/components/icons/lock-icon";
import { MembershipModal } from "../membership-modal";

export const PostCardMemberOnlyOverlay = ({
  vtuberId,
}: {
  vtuberId: bigint;
}) => {
  const { getText } = useLanguage();
  return (
    <div className="inset-0 absolute z-20 bg-[hsl(221, 27%, 26%, 10%)] flex items-center justify-center backdrop-blur-sm">
      <div className="flex flex-col items-center gap-y-3">
        <p className="font-bold">{getText("membership_only_content")}</p>
        <MembershipModal
          vtuberId={vtuberId}
          asChild>
          <Button className="bg-gradient-2 font-bold gap-x-3 h-12 px-8 text-white">
            <LockIcon className="mr-2" />
            {getText("view_paid_post")}
          </Button>
        </MembershipModal>
      </div>
    </div>
  );
};
