import { timestampDate } from "@bufbuild/protobuf/wkt";
import { useSuspenseQuery } from "@tanstack/react-query";
import { Link } from "@tanstack/react-router";
import { useCategories } from "@vtuber/auth/hooks";
import { useLanguage } from "@vtuber/language/hooks";
import { AspectRatio } from "@vtuber/ui/components/aspect-ratio";
import { Image } from "@vtuber/ui/components/image";
import { MarkDown } from "@vtuber/ui/components/markdown";
import { Paginator } from "@vtuber/ui/components/paginator";
import { Progress } from "@vtuber/ui/components/progress";
import { RemainingDays } from "@vtuber/ui/components/remaining-days";
import { Tag } from "@vtuber/ui/components/tag";
import { getProgress } from "@vtuber/ui/lib/utils";
import { useState } from "react";
import { vtuberCampaignsQueryOptions } from "~/utils/api";

export const VtuberCampaigns = ({ vtuberId }: { vtuberId: bigint }) => {
  const { getMultipleCategories } = useCategories();
  const { getText } = useLanguage();
  const [page, setPage] = useState(0);
  const progress = (total: number, raised: number) =>
    getProgress(total, raised);
  const { data, error } = useSuspenseQuery(
    vtuberCampaignsQueryOptions({
      vtuberId,
      size: 3,
      page,
    }),
  );

  const campaigns = data?.data || [];

  if (error)
    return (
      <div className="h-40 flex items-center justify-center">
        {error.message}
      </div>
    );

  if (!campaigns || campaigns.length === 0)
    return (
      <div className="h-40 flex items-center justify-center">
        {getText("no_campaigns_for_this_vtuber")}
      </div>
    );

  return (
    <div className="space-y-10">
      <div className="space-y-14">
        <h3 className="text-h2-pc font-bold leading-normal text-center">
          {getText("all_campaigns")}
        </h3>

        {campaigns.map((c) => {
          const tags = getMultipleCategories(c.categories);
          return (
            <Link
              to="/campaign/$id"
              params={{
                id: c.slug,
              }}
              key={c.id}
              className="grid grid-cols-12 items-center gap-14">
              <div className="col-span-5">
                <AspectRatio ratio={16 / 9}>
                  <Image
                    src={c.thumbnail}
                    alt={c.name}
                    className="size-full rounded-[8px]"
                  />
                </AspectRatio>
              </div>
              <section className="col-span-7 space-y-[22px]">
                <h3 className="font-bold sm:text-[33px] text-[23px] text-font">
                  {c.name}
                </h3>
                <div className="flex sm:items-center items-start sm:flex-row flex-col sm:gap-x-6 sm:gap-y-0 gap-y-[11px]">
                  <p className="text-font text-sm font-medium">
                    {timestampDate(c.startDate!).toLocaleDateString()} -{" "}
                    {timestampDate(c.endDate!).toLocaleDateString()}{" "}
                  </p>
                  {/* {c.categoryId && ( 
              <Tag
                className="text-white py-1 sm:px-3 px-2 sm:text-sm text-xs font-medium"
                variant={"success"}>
                hello
                {getCategoryById(c.categoryId)?.name}
              </Tag>
              )}  */}
                </div>
                <div>
                  <div className="flex sm:items-center sm:flex-row flex-col items-start sm:justify-between justify-start w-full">
                    <h3 className="font-bold sm:text-[33px] text-[23px] text-font">
                      {c.totalBudget.toLocaleString()}{" "}
                      <span className="text-[22px]">{getText("yen")}</span>
                    </h3>
                    <RemainingDays
                      className="sm:text-[17px] text-[10px] font-medium"
                      daysClassname="sm:text-[34px] text-[22px] font-bold"
                      endDate={c.endDate!}
                      startDate={c.startDate!}
                    />
                  </div>
                  <Progress
                    value={progress(c.totalBudget, c.totalRaised)}
                    className="h-[10px]"
                  />
                </div>
                <MarkDown
                  markdown={c.shortDescription}
                  className="font-medium text-font"
                />
                {tags.length > 0 && (
                  <div className="flex items-center gap-3 flex-wrap">
                    {tags.map((t) => (
                      <Tag
                        key={t?.id}
                        variant={"outline-white"}
                        className=" py-1 px-3 w-max">
                        {t?.name}
                      </Tag>
                    ))}
                  </div>
                )}
              </section>
            </Link>
          );
        })}
      </div>
      <Paginator
        withSearchQuery={false}
        onPageChange={setPage}
        currentPage={page}
        totalPages={data?.paginationDetails?.totalPages}
      />
    </div>
  );
};
