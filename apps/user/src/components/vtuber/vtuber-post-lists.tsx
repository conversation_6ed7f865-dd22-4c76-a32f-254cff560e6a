import { useSuspenseInfiniteQuery } from "@tanstack/react-query";
import { useLanguage } from "@vtuber/language/hooks";
import { VtuberProfile } from "@vtuber/services/vtubers";
import useIntersectionObserver from "@vtuber/ui/hooks/use-intersection-observer";
import { vtuberPostQueryOptions } from "~/utils/api";
import { PostCard } from "../post/post-card";
import { PostCardSkeleton } from "../skeletons/post-card-skeleton";

interface Props {
  id: string;
  vtuber: VtuberProfile;
}

export const VtuberPostList = ({ id, vtuber }: Props) => {
  const { getText } = useLanguage();

  const { data, isFetchingNextPage, hasNextPage, fetchNextPage } =
    useSuspenseInfiniteQuery(
      vtuberPostQueryOptions({
        username: id,
        size: 2,
      }),
    );

  const post = data.pages.flatMap((page) => page?.data || []);

  const postAvalaible = post && post.length > 0;

  const { ref: loader } = useIntersectionObserver({
    rootMargin: "100px",
    onChange: (intersecting) => {
      if (intersecting && hasNextPage && !isFetchingNextPage) {
        fetchNextPage();
      }
    },
  });

  return (
    <section>
      {postAvalaible ? (
        <div className="space-y-10">
          {post.map((p) => (
            <PostCard
              shouldBlur={p.membershipOnly && !vtuber.isUserSubscribed}
              key={p.id}
              post={p}
              vtuberId={vtuber.id}
            />
          ))}
          {isFetchingNextPage && <PostCardSkeleton />}
          <div ref={loader} />
        </div>
      ) : (
        <div className="h-40 flex items-center justify-center text-3xl font-semibold">
          {getText("no_posts_for_this_vtuber")}
        </div>
      )}
    </section>
  );
};
