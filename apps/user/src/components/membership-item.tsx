import { useLocation, useRouter } from "@tanstack/react-router";
import { useAuth } from "@vtuber/auth/hooks";
import { useLanguage } from "@vtuber/language/hooks";
import { VtuberPlan } from "@vtuber/services/vtubers";
import { Button } from "@vtuber/ui/components/button";
import { MarkDown } from "@vtuber/ui/components/markdown";
import { cn } from "@vtuber/ui/lib/utils";
import { AnimatePresence, motion } from "motion/react";

interface Props {
  data: VtuberPlan;
  index: number;
  tabValue: "annual" | "monthly";
}

export const MembershipItem = ({ data, index, tabValue }: Props) => {
  const { getText } = useLanguage();
  const { session } = useAuth();
  const router = useRouter();
  const { pathname } = useLocation();
  return (
    <div className="flex-col flex h-full">
      {index === 1 && (
        <div className="text-[#2A8796] bg-[#B3DBDE] rounded-t-[10px] text-center text-lg font-bold h-[43px] flex items-center justify-center">
          {getText("most_popular")}
        </div>
      )}

      <div
        className={cn(
          "text-font rounded-10 flex-1 flex flex-col",
          index === 1
            ? "bg-gradient-1 p-0.5 pt-0 rounded-10 rounded-t-none"
            : "md:mt-10",
        )}>
        <div
          className={cn(
            "space-y-[30px] h-full py-[29px] px-6",
            index !== 1
              ? "border border-white rounded-10 md:bg-transparent bg-gradient-6"
              : "rounded-10 rounded-t-none bg-gradient-6",
          )}>
          <h4 className="text-xl font-bold text-center">{data.title}</h4>
          <section className="text-center">
            <AnimatePresence mode="wait">
              {tabValue === "monthly" && (
                <motion.h5
                  key={"monthly"}
                  initial={{ opacity: 0, scale: 0.5 }}
                  animate={{
                    opacity: 1,
                    scale: 1,
                    transition: { duration: 0.1 },
                  }}
                  exit={{
                    opacity: 0,
                    scale: 0.5,
                    transition: { duration: 0.1 },
                  }}
                  className="text-5xl font-semibold font-montserrat">
                  {data.price}{" "}
                  <span className="text-base font-normal font-noto">
                    {getText("yen")}
                  </span>
                </motion.h5>
              )}
              {tabValue === "annual" && (
                <motion.h5
                  key={"annual"}
                  initial={{ opacity: 0, scale: 0.5 }}
                  animate={{
                    opacity: 1,
                    scale: 1,
                    transition: { duration: 0.1 },
                  }}
                  exit={{
                    opacity: 0,
                    scale: 0.5,
                    transition: { duration: 0.1 },
                  }}
                  className="text-5xl font-semibold font-montserrat">
                  {data.annualPrice}{" "}
                  <span className="text-base font-normal font-noto">
                    {getText("yen")}
                  </span>
                </motion.h5>
              )}
            </AnimatePresence>
            <p className="text-xs font-medium">{data.shortDescription}</p>
          </section>
          <section className="flex justify-center">
            <Button
              onClick={() => {
                if (!session) {
                  router.navigate({
                    to: "/login",
                    search: (prev) => ({
                      ...prev,
                      redirect: pathname,
                    }),
                  });
                  return;
                }
                router.navigate({
                  to: "/payment",
                  search: {
                    price: data.price,
                    subscriptionId: Number(data.id),
                    isUserSubscription: true,
                  },
                  mask: {
                    to: "/payment",
                    search: {
                      price: undefined,
                      subscriptionId: undefined,
                      isUserSubscription: undefined,
                    },
                  },
                });
              }}
              disabled={data.isSubscribed}
              className={cn(
                "rounded-full h-[37px] w-[191px] px-8 py-[9px] ",
                index > 0
                  ? "font-bold text-sm text-white bg-gradient-2"
                  : "text-[#A9A9A9] bg-[#EBEBEB] hover:bg-[#EBEBEB]/80 hover:text-black text-xs font-medium",
              )}
              size={"lg"}>
              {!session
                ? getText("login")
                : data.isSubscribed
                  ? getText("currently_in_use")
                  : getText("upgrade")}
            </Button>
          </section>
          <section>
            <MarkDown markdown={data.description} />
          </section>
        </div>
      </div>
    </div>
  );
};
