import { infiniteQueryOptions, queryOptions } from "@tanstack/react-query";
import { GetAllCampaignsResponse } from "@vtuber/services/campaigns";
import {
  billingClient,
  campaignClient,
  eventClient,
  eventParticipantClient,
  favoriteVtuberClient,
  postsClient,
  vtuberGalleryClient,
  vtuberProfilesClient,
} from "@vtuber/services/client";
import { GetAllEventsResponse } from "@vtuber/services/events";
import { PaginationRequest } from "@vtuber/services/shared";
import { GetAllVtuberProfilesResponse } from "@vtuber/services/vtubers";

export const billingQueryOptions = queryOptions({
  queryKey: ["credit_card"],
  queryFn: async () => {
    const [data] = await billingClient.getBillingInfo({});
    return data;
  },
  staleTime: Infinity,
});

export const campaignsQueryOptions = ({
  enabled = true,
  initialData,
  ...props
}: Omit<PaginationRequest, "$typeName"> & {
  enabled?: boolean;
  initialData?: GetAllCampaignsResponse;
  categoryId?: bigint;
}) =>
  queryOptions({
    queryKey: ["campaigns", props],
    queryFn: async () => {
      const [data] = await campaignClient.getAllCampaigns({
        categoryId: props.categoryId,
        pagination: {
          ...props,
          sort: "created_at",
          order: "desc",
        },
      });
      return data;
    },
    enabled,
    initialData,
  });

export const eventsQueryOptions = ({
  enabled = true,
  initialData,
  ...props
}: Omit<PaginationRequest, "$typeName"> & {
  enabled?: boolean;
  initialData?: GetAllEventsResponse;
  categoryId?: bigint;
}) =>
  queryOptions({
    queryKey: ["events", props],
    queryFn: async () => {
      const [data] = await eventClient.getAllEvents({
        pagination: props,
        categoryId: props.categoryId,
      });
      return data;
    },
    initialData,
    enabled,
  });

export const vtuberProfileQueryOptions = ({
  enabled = true,
  initialData,

  ...props
}: Omit<PaginationRequest, "$typeName"> & {
  enabled?: boolean;
  initialData?: GetAllVtuberProfilesResponse;
  categoryId?: bigint;
}) =>
  queryOptions({
    queryKey: ["vtubers", props],
    queryFn: async () => {
      const [data] = await vtuberProfilesClient.getAllVtuberProfiles({
        pagination: props,
        categoryId: props.categoryId,
      });
      return data;
    },
    initialData,
    enabled,
  });

export const vtuberPostQueryOptions = ({
  username,
  ...pagination
}: Omit<PaginationRequest, "$typeName" | "page"> & {
  username: string;
}) => {
  const options = infiniteQueryOptions({
    queryKey: ["vtuber_posts", username, pagination],
    queryFn: async ({ pageParam = 1 }) => {
      const [data] = await postsClient.getAllPosts({
        vtuberUsername: username,
        pagination: { ...pagination, page: pageParam },
      });
      return data;
    },
    initialPageParam: 0,
    getNextPageParam: (paginate) => {
      const nextPage = paginate?.paginationDetails?.nextPage;
      const totalPages = paginate?.paginationDetails?.totalPages;
      const currentPage = paginate?.paginationDetails?.currentPage;
      if (currentPage === totalPages) return undefined;
      return nextPage;
    },
  });

  return options;
};

export const myFavoriteVtubersQueryOptions = ({
  enabled = true,
  ...props
}: Omit<PaginationRequest, "$typeName"> & {
  enabled?: boolean;
}) =>
  queryOptions({
    queryKey: ["my_favorite_vtubers", props],
    queryFn: async () => {
      const [data] = await favoriteVtuberClient.getAllFavoriteVtuber({
        pagination: props,
      });
      return data;
    },
    enabled,
  });
export const recommendedPostsQueryOptions = ({
  // vtuberId,
  ...props
}: Omit<PaginationRequest, "$typeName"> & {
  // vtuberId: string;
}) =>
  queryOptions({
    queryKey: ["recommended_posts", props],
    // queryKey: ["recommended_posts", props, vtuberId],
    queryFn: async () => {
      const [data] = await postsClient.getAllPosts({
        // vtuberId,
        pagination: props,
      });
      return data;
    },
  });

export const mySupportedCampaignsQueryOptions = ({
  enabled = true,
  ...props
}: Omit<PaginationRequest, "$typeName"> & {
  enabled?: boolean;
}) =>
  queryOptions({
    queryKey: ["my_supported_campaigns", props],
    queryFn: async () => {
      const [data] = await campaignClient.getMySupportedCampaigns({
        pagination: props,
      });
      return data;
    },
    enabled,
  });

export const vtuberCampaignsQueryOptions = ({
  vtuberId,
  enabled = true,
  initialData,
  ...pagination
}: Omit<PaginationRequest, "$typeName"> & {
  vtuberId: bigint;
  enabled?: boolean;
  initialData?: GetAllCampaignsResponse;
}) => {
  const options = queryOptions({
    queryKey: ["vtuber_campaigns", pagination],
    queryFn: async () => {
      const [data] = await campaignClient.getAllCampaignsByVtuberId({
        vtuberId,
        pagination,
      });
      return data;
    },
    enabled,
    initialData,
  });
  return options;
};

export const vtuberParticipatingEventsQueryOptions = ({
  vtuberId,
  enabled = true,
  ...pagination
}: Omit<PaginationRequest, "$typeName"> & {
  vtuberId: bigint;
  enabled?: boolean;
}) => {
  const options = queryOptions({
    queryKey: ["vtuber_participating_events", pagination, vtuberId],
    queryFn: async () => {
      const [data] =
        await eventParticipantClient.getAllEventParticipationOfVtuber({
          vtuberId,
          pagination,
        });
      return data;
    },
    enabled,
  });
  return options;
};

export const vtuberEventsQueryOptions = ({
  vtuberId,
  enabled = true,
  ...pagination
}: Omit<PaginationRequest, "$typeName"> & {
  vtuberId: bigint;
  enabled?: boolean;
}) => {
  const options = queryOptions({
    queryKey: ["vtuber_events", pagination, vtuberId],
    queryFn: async () => {
      const [data] = await eventClient.getAllEvents({
        pagination: {
          ...pagination,
          vtuberId,
        },
      });
      return data;
    },
    enabled,
  });
  return options;
};

export const vtuberGalleryQueryOptions = ({
  vtuberId,
  enabled = true,
  ...pagination
}: Omit<PaginationRequest, "$typeName"> & {
  vtuberId: bigint;
  enabled?: boolean;
}) => {
  const options = queryOptions({
    queryKey: ["vtuber_gallery", pagination],
    queryFn: async () => {
      const [data] = await vtuberGalleryClient.getVtuberGalleries({
        vtuberId,
        pagination: {
          sort: "created_at",
          order: "desc",
          ...pagination,
        },
      });
      return data;
    },
    enabled,
  });
  return options;
};
