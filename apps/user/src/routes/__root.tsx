import { TransportProvider } from "@connectrpc/connect-query";
import type { QueryClient } from "@tanstack/react-query";
import {
  createRootRouteWithContext,
  HeadContent,
  Outlet,
  Scripts,
} from "@tanstack/react-router";
import { AuthProvider, CategoriesProvider } from "@vtuber/auth/provider";
import { LanguageProvider } from "@vtuber/language/provider";
import {
  authClient,
  categoryClient,
  staticClient,
  transport,
} from "@vtuber/services/client";
import { DefaultCatchBoundary } from "@vtuber/ui/components/default-catch-boundry";
import { NavigationProgress } from "@vtuber/ui/components/navigation-progress";
import { useNetworkStatus } from "@vtuber/ui/hooks/use-network-status";
import * as React from "react";

import { LanguageKey } from "@vtuber/language/types";
import css from "@vtuber/ui/globals.css?url";
import { Toaster } from "sonner";
import { seo } from "~/utils/seo";

const gtmID = process.env.GOOGLE_TAG_MANAGER_ID;

export const Route = createRootRouteWithContext<{
  queryClient: QueryClient;
}>()({
  head: () => ({
    meta: [
      {
        charSet: "utf-8",
      },
      {
        name: "viewport",
        content: "width=device-width, initial-scale=1",
      },
      ...seo({
        title: "V-SAI",
        description: "a place where VTubers and fans can connect",
      }),
    ],
    links: [
      { rel: "stylesheet", href: css },
      {
        rel: "apple-touch-icon",
        sizes: "180x180",
        href: "/apple-touch-icon.png",
      },
      {
        rel: "icon",
        type: "image/png",
        sizes: "32x32",
        href: "/favicon-32x32.png",
      },
      {
        rel: "icon",
        type: "image/png",
        sizes: "16x16",
        href: "/favicon-16x16.png",
      },
      { rel: "manifest", href: "/site.webmanifest", color: "#fffff" },
      { rel: "icon", href: "/favicon.ico" },
    ],
  }),
  errorComponent: (props) => {
    return (
      <RootDocument>
        <DefaultCatchBoundary {...props} />
      </RootDocument>
    );
  },
  component: RootComponent,
  loader: async () => {
    const [categories] = await categoryClient.getAllCategories({});
    const language = getCookie("language");
    return {
      language: (["en", "ja"].includes(language)
        ? language
        : "ja") as LanguageKey,
      categories,
    };
  },
  beforeLoad: async () => {
    const [user] = await authClient.getSession({});
    const [links] = await staticClient.getSocialMediaLinks({});
    return {
      user,
      links,
    };
  },
  staleTime: 60000,
});

function RootComponent() {
  const { user } = Route.useRouteContext();
  const { language, categories } = Route.useLoaderData();
  useNetworkStatus();

  return (
    <RootDocument>
      <TransportProvider transport={transport}>
        <AuthProvider user={user}>
          <LanguageProvider defaultLanguage={language || "ja"}>
            <CategoriesProvider categories={categories?.categories || null}>
              <Toaster
                richColors
                position="bottom-left"
              />
              <NavigationProgress />
              <Outlet />
            </CategoriesProvider>
          </LanguageProvider>
        </AuthProvider>
      </TransportProvider>
    </RootDocument>
  );
}

function RootDocument({ children }: { children: React.ReactNode }) {
  return (
    <html className="light">
      <head>
        <HeadContent />

        <script
          dangerouslySetInnerHTML={{
            __html: `(function(w,d,s,l,i){w[l]=w[l]||[];w[l].push({'gtm.start':
new Date().getTime(),event:'gtm.js'});var f=d.getElementsByTagName(s)[0],
j=d.createElement(s),dl=l!='dataLayer'?'&l='+l:'';j.async=true;j.src=
'https://www.googletagmanager.com/gtm.js?id='+i+dl;f.parentNode.insertBefore(j,f);
})(window,document,'script','dataLayer','${gtmID}');`,
          }}
        />
      </head>
      <body>
        <noscript>
          <iframe
            src={`https://www.googletagmanager.com/ns.html?id=${gtmID}`}
            height="0"
            width="0"
            style={{
              display: "none",
              visibility: "hidden",
            }}
          />
        </noscript>
        {children}
        <Scripts />
      </body>
    </html>
  );
}
