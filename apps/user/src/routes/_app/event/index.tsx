import { createFileRoute, notFound, useNavigate } from "@tanstack/react-router";
import { useCategories } from "@vtuber/auth/hooks";
import { useLanguage } from "@vtuber/language/hooks";
import { eventClient } from "@vtuber/services/client";
import { Button } from "@vtuber/ui/components/button";
import { Container } from "@vtuber/ui/components/container";
import { ContentHeading } from "@vtuber/ui/components/content-heading";
import { ScrollArea, ScrollBar } from "@vtuber/ui/components/scroll-area";
import { useIsMobile } from "@vtuber/ui/hooks/use-mobile";
import { truncateString } from "@vtuber/ui/lib/truncate-string";
import { useState } from "react";
import { z } from "zod";
import { CategorySearch } from "~/components/category-search";
import { EventsList } from "~/components/events/events-list";
import { CallToAction } from "~/components/layout/call-to-action";
import { KeywordSearch } from "~/components/layout/keyword-search";
import { PageTitle } from "~/components/layout/page-title";
import { validatePagination } from "~/data/constants";

export const Route = createFileRoute("/_app/event/")({
  component: RouteComponent,
  validateSearch: validatePagination.extend({
    catId: z.number().optional(),
  }),
  loader: async () => {
    const [events, err] = await eventClient.getAllEvents({
      pagination: {
        sort: "created_at",
      },
    });

    if (err) {
      throw notFound({
        data: err.rawMessage,
      });
    }

    return events;
  },
});

function RouteComponent() {
  const navigate = useNavigate({ from: Route.fullPath });
  const { catId: caterogyId } = Route.useSearch();
  const { getText } = useLanguage();
  const [catId, setCatId] = useState(caterogyId);
  const { isMobile } = useIsMobile();
  const events = Route.useLoaderData();
  const { categories } = useCategories();

  return (
    <div className="relative pt-20">
      <div className="grid gap-y-28">
        <Container className="grid gap-y-16">
          <PageTitle title="events" />
          <div className="grid sm:gap-y-16 gap-y-12">
            <ScrollArea className="overflow-hidden">
              <section className="flex items-center md:flex-wrap flex-nowrap gap-4 justify-center">
                <Button
                  className="rounded-full"
                  onClick={() => {
                    setCatId(undefined);
                    navigate({
                      search: {
                        catId: undefined,
                      },
                    });
                  }}
                  variant={!catId ? "default" : "outline"}>
                  {getText("all")}
                </Button>
                {categories.filter(Boolean).map((c) => (
                  <Button
                    className="rounded-full"
                    onClick={() => {
                      setCatId(Number(c.id));
                      navigate({
                        search: {
                          catId: Number(c.id),
                        },
                      });
                    }}
                    variant={catId === Number(c.id) ? "default" : "outline"}
                    key={c.id.toString()}>
                    {isMobile ? `${truncateString(c.name, 10)}...` : c.name}
                  </Button>
                ))}
              </section>

              <ScrollBar orientation="horizontal" />
            </ScrollArea>
            <EventsList
              events={events}
              catId={catId}
            />
          </div>
          <section className="space-y-12 sm:pt-24 pt-8">
            <ContentHeading
              title="keyword_category_search"
              subTitle="Keyword Category Search"
              className="text-balance"
            />
            <div className="sm:space-y-12 space-y-20">
              <KeywordSearch />
              <CategorySearch />
            </div>
          </section>
        </Container>
        <CallToAction />
      </div>
    </div>
  );
}
