import { createFileRoute, notFound, useNavigate } from "@tanstack/react-router";
import { useCategories } from "@vtuber/auth/hooks";
import { useLanguage } from "@vtuber/language/hooks";
import { campaignClient } from "@vtuber/services/client";
import { But<PERSON> } from "@vtuber/ui/components/button";
import { Container } from "@vtuber/ui/components/container";
import { ContentHeading } from "@vtuber/ui/components/content-heading";
import { ScrollArea, ScrollBar } from "@vtuber/ui/components/scroll-area";
import { useIsMobile } from "@vtuber/ui/hooks/use-mobile";
import { truncateString } from "@vtuber/ui/lib/truncate-string";
import { useState } from "react";
import { z } from "zod";
import { CampaignList } from "~/components/campaign/campaign-list";
import { CategorySearch } from "~/components/category-search";
import { CallToAction } from "~/components/layout/call-to-action";
import { KeywordSearch } from "~/components/layout/keyword-search";
import { PageTitle } from "~/components/layout/page-title";
import { validatePagination } from "~/data/constants";

const SearchSchema = validatePagination.extend({
  catId: z.number().optional(),
});

export const Route = createFileRoute("/_app/campaign/")({
  component: RouteComponent,
  validateSearch: SearchSchema,
  loader: async () => {
    const [campaigns, err] = await campaignClient.getAllCampaigns({
      pagination: {
        size: 9,
      },
    });

    if (err) {
      throw notFound({
        data: err.rawMessage,
      });
    }

    return campaigns;
  },
});

function RouteComponent() {
  const campaigns = Route.useLoaderData();
  const { catId: caterogyId } = Route.useSearch();
  const { categories } = useCategories();
  const { isMobile } = useIsMobile();
  const navigate = useNavigate({ from: Route.fullPath });
  const [catId, setCatId] = useState(caterogyId);
  const { getText } = useLanguage();

  return (
    <div className="relative pt-20">
      <div className="grid gap-y-28">
        <Container className="flex flex-col gap-y-16">
          <PageTitle title="crowdfunding" />
          <div className="grid sm:gap-y-16 gap-y-12">
            <ScrollArea className="overflow-hidden">
              <section className="flex items-center md:flex-wrap flex-nowrap gap-4 justify-center">
                <Button
                  className="rounded-full"
                  onClick={() => {
                    setCatId(undefined);
                    navigate({
                      search: {
                        catId: undefined,
                      },
                    });
                  }}
                  variant={!catId ? "default" : "outline"}>
                  {getText("all")}
                </Button>
                {categories.filter(Boolean).map((c) => (
                  <Button
                    className="rounded-full"
                    onClick={() => {
                      setCatId(Number(c.id));
                      navigate({
                        search: {
                          catId: Number(c.id),
                        },
                      });
                    }}
                    variant={catId === Number(c.id) ? "default" : "outline"}
                    key={c.id.toString()}>
                    {isMobile ? `${truncateString(c.name, 10)}...` : c.name}
                  </Button>
                ))}
              </section>

              <ScrollBar orientation="horizontal" />
            </ScrollArea>
            <CampaignList
              initialData={campaigns}
              catId={catId}
            />
          </div>
          <section className="space-y-12 sm:pt-24 pt-8">
            <ContentHeading
              title="keyword_category_search"
              subTitle="Keyword Category Search"
              className="text-balance"
            />
            <div className="sm:space-y-12 space-y-20">
              <KeywordSearch />
              <CategorySearch />
            </div>
          </section>
        </Container>
        <CallToAction />
      </div>
    </div>
  );
}
