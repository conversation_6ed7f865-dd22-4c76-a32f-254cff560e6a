// @generated by protoc-gen-es v2.5.2 with parameter "target=ts"
// @generated from file campaigns/v1/campaignvariant.proto (package api.campaigns.v1, syntax proto3)
/* eslint-disable */

import type { GenFile, GenMessage, GenService } from "@bufbuild/protobuf/codegenv2";
import { fileDesc, messageDesc, serviceDesc } from "@bufbuild/protobuf/codegenv2";
import { file_authz_v1_authz } from "../../authz/v1/authz_pb";
import type { Timestamp } from "@bufbuild/protobuf/wkt";
import { file_google_protobuf_timestamp } from "@bufbuild/protobuf/wkt";
import { file_shared_v1_generic } from "../../shared/v1/generic_pb";
import type { PaginationDetails, PaginationRequest } from "../../shared/v1/pagination_pb";
import { file_shared_v1_pagination } from "../../shared/v1/pagination_pb";
import type { Message } from "@bufbuild/protobuf";

/**
 * Describes the file campaigns/v1/campaignvariant.proto.
 */
export const file_campaigns_v1_campaignvariant: GenFile = /*@__PURE__*/
  fileDesc("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", [file_authz_v1_authz, file_google_protobuf_timestamp, file_shared_v1_generic, file_shared_v1_pagination]);

/**
 * @generated from message api.campaigns.v1.AddCampaignVariantRequest
 */
export type AddCampaignVariantRequest = Message<"api.campaigns.v1.AddCampaignVariantRequest"> & {
  /**
   * @gotag: validate:"required"
   *
   * @generated from field: int64 campaign_id = 1;
   */
  campaignId: bigint;

  /**
   * @gotag: validate:"gte=1"
   *
   * @generated from field: int32 price = 2;
   */
  price: number;

  /**
   * @gotag: validate:"required"
   *
   * @generated from field: string description = 3;
   */
  description: string;

  /**
   * @gotag: validate:"gte=0"
   *
   * @generated from field: int32 max_sub = 4;
   */
  maxSub: number;

  /**
   * @gotag: validate:"required"
   *
   * @generated from field: string image = 5;
   */
  image: string;

  /**
   * @gotag: validate:"required"
   *
   * @generated from field: string title = 6;
   */
  title: string;
};

/**
 * Describes the message api.campaigns.v1.AddCampaignVariantRequest.
 * Use `create(AddCampaignVariantRequestSchema)` to create a new message.
 */
export const AddCampaignVariantRequestSchema: GenMessage<AddCampaignVariantRequest> = /*@__PURE__*/
  messageDesc(file_campaigns_v1_campaignvariant, 0);

/**
 * @generated from message api.campaigns.v1.GetCampaignSubResponse
 */
export type GetCampaignSubResponse = Message<"api.campaigns.v1.GetCampaignSubResponse"> & {
  /**
   * @generated from field: repeated api.campaigns.v1.SubDetails subs = 1;
   */
  subs: SubDetails[];

  /**
   * @generated from field: optional api.shared.v1.PaginationDetails pagination = 2;
   */
  pagination?: PaginationDetails;
};

/**
 * Describes the message api.campaigns.v1.GetCampaignSubResponse.
 * Use `create(GetCampaignSubResponseSchema)` to create a new message.
 */
export const GetCampaignSubResponseSchema: GenMessage<GetCampaignSubResponse> = /*@__PURE__*/
  messageDesc(file_campaigns_v1_campaignvariant, 1);

/**
 * @generated from message api.campaigns.v1.SubDetails
 */
export type SubDetails = Message<"api.campaigns.v1.SubDetails"> & {
  /**
   * @generated from field: int64 id = 1;
   */
  id: bigint;

  /**
   * @generated from field: string name = 2;
   */
  name: string;

  /**
   * @generated from field: optional string image = 3;
   */
  image?: string;

  /**
   * @generated from field: int32 amount = 4;
   */
  amount: number;
};

/**
 * Describes the message api.campaigns.v1.SubDetails.
 * Use `create(SubDetailsSchema)` to create a new message.
 */
export const SubDetailsSchema: GenMessage<SubDetails> = /*@__PURE__*/
  messageDesc(file_campaigns_v1_campaignvariant, 2);

/**
 * @generated from message api.campaigns.v1.GetCampaignSubRequest
 */
export type GetCampaignSubRequest = Message<"api.campaigns.v1.GetCampaignSubRequest"> & {
  /**
   * @gotag: validate:"required"
   *
   * @generated from field: int64 id = 1;
   */
  id: bigint;

  /**
   * @generated from field: optional api.shared.v1.PaginationRequest pagination = 2;
   */
  pagination?: PaginationRequest;
};

/**
 * Describes the message api.campaigns.v1.GetCampaignSubRequest.
 * Use `create(GetCampaignSubRequestSchema)` to create a new message.
 */
export const GetCampaignSubRequestSchema: GenMessage<GetCampaignSubRequest> = /*@__PURE__*/
  messageDesc(file_campaigns_v1_campaignvariant, 3);

/**
 * @generated from message api.campaigns.v1.AddCampaignVariantResponse
 */
export type AddCampaignVariantResponse = Message<"api.campaigns.v1.AddCampaignVariantResponse"> & {
  /**
   * @generated from field: api.campaigns.v1.CampaignVariant data = 1;
   */
  data?: CampaignVariant;
};

/**
 * Describes the message api.campaigns.v1.AddCampaignVariantResponse.
 * Use `create(AddCampaignVariantResponseSchema)` to create a new message.
 */
export const AddCampaignVariantResponseSchema: GenMessage<AddCampaignVariantResponse> = /*@__PURE__*/
  messageDesc(file_campaigns_v1_campaignvariant, 4);

/**
 * @generated from message api.campaigns.v1.CampaignVariant
 */
export type CampaignVariant = Message<"api.campaigns.v1.CampaignVariant"> & {
  /**
   * @generated from field: int64 id = 1;
   */
  id: bigint;

  /**
   * @generated from field: int64 campaign_id = 2;
   */
  campaignId: bigint;

  /**
   * @generated from field: int32 price = 3;
   */
  price: number;

  /**
   * @generated from field: string description = 4;
   */
  description: string;

  /**
   * @generated from field: int32 max_sub = 5;
   */
  maxSub: number;

  /**
   * @generated from field: string image = 6;
   */
  image: string;

  /**
   * @generated from field: google.protobuf.Timestamp created_at = 7;
   */
  createdAt?: Timestamp;

  /**
   * @generated from field: int64 sub_count = 8;
   */
  subCount: bigint;

  /**
   * @generated from field: string title = 9;
   */
  title: string;
};

/**
 * Describes the message api.campaigns.v1.CampaignVariant.
 * Use `create(CampaignVariantSchema)` to create a new message.
 */
export const CampaignVariantSchema: GenMessage<CampaignVariant> = /*@__PURE__*/
  messageDesc(file_campaigns_v1_campaignvariant, 5);

/**
 * @generated from message api.campaigns.v1.GetAllCampaignVariantsRequest
 */
export type GetAllCampaignVariantsRequest = Message<"api.campaigns.v1.GetAllCampaignVariantsRequest"> & {
  /**
   * @gotag: validate:"required"
   *
   * @generated from field: int64 campaign_id = 1;
   */
  campaignId: bigint;
};

/**
 * Describes the message api.campaigns.v1.GetAllCampaignVariantsRequest.
 * Use `create(GetAllCampaignVariantsRequestSchema)` to create a new message.
 */
export const GetAllCampaignVariantsRequestSchema: GenMessage<GetAllCampaignVariantsRequest> = /*@__PURE__*/
  messageDesc(file_campaigns_v1_campaignvariant, 6);

/**
 * @generated from message api.campaigns.v1.GetAllCampaignVariantsResponse
 */
export type GetAllCampaignVariantsResponse = Message<"api.campaigns.v1.GetAllCampaignVariantsResponse"> & {
  /**
   * @generated from field: repeated api.campaigns.v1.CampaignVariant data = 1;
   */
  data: CampaignVariant[];
};

/**
 * Describes the message api.campaigns.v1.GetAllCampaignVariantsResponse.
 * Use `create(GetAllCampaignVariantsResponseSchema)` to create a new message.
 */
export const GetAllCampaignVariantsResponseSchema: GenMessage<GetAllCampaignVariantsResponse> = /*@__PURE__*/
  messageDesc(file_campaigns_v1_campaignvariant, 7);

/**
 * @generated from message api.campaigns.v1.GetCampaignVariantByIdRequest
 */
export type GetCampaignVariantByIdRequest = Message<"api.campaigns.v1.GetCampaignVariantByIdRequest"> & {
  /**
   * @gotag: validate:"required"
   *
   * @generated from field: int64 id = 1;
   */
  id: bigint;
};

/**
 * Describes the message api.campaigns.v1.GetCampaignVariantByIdRequest.
 * Use `create(GetCampaignVariantByIdRequestSchema)` to create a new message.
 */
export const GetCampaignVariantByIdRequestSchema: GenMessage<GetCampaignVariantByIdRequest> = /*@__PURE__*/
  messageDesc(file_campaigns_v1_campaignvariant, 8);

/**
 * @generated from message api.campaigns.v1.GetCampaignVariantByIdResponse
 */
export type GetCampaignVariantByIdResponse = Message<"api.campaigns.v1.GetCampaignVariantByIdResponse"> & {
  /**
   * @generated from field: api.campaigns.v1.CampaignVariant data = 1;
   */
  data?: CampaignVariant;
};

/**
 * Describes the message api.campaigns.v1.GetCampaignVariantByIdResponse.
 * Use `create(GetCampaignVariantByIdResponseSchema)` to create a new message.
 */
export const GetCampaignVariantByIdResponseSchema: GenMessage<GetCampaignVariantByIdResponse> = /*@__PURE__*/
  messageDesc(file_campaigns_v1_campaignvariant, 9);

/**
 * @generated from message api.campaigns.v1.DeleteCampaignVariantByIdRequest
 */
export type DeleteCampaignVariantByIdRequest = Message<"api.campaigns.v1.DeleteCampaignVariantByIdRequest"> & {
  /**
   * @gotag: validate:"required"
   *
   * @generated from field: int64 id = 1;
   */
  id: bigint;
};

/**
 * Describes the message api.campaigns.v1.DeleteCampaignVariantByIdRequest.
 * Use `create(DeleteCampaignVariantByIdRequestSchema)` to create a new message.
 */
export const DeleteCampaignVariantByIdRequestSchema: GenMessage<DeleteCampaignVariantByIdRequest> = /*@__PURE__*/
  messageDesc(file_campaigns_v1_campaignvariant, 10);

/**
 * @generated from message api.campaigns.v1.UpdateCampaignVariantByIdRequest
 */
export type UpdateCampaignVariantByIdRequest = Message<"api.campaigns.v1.UpdateCampaignVariantByIdRequest"> & {
  /**
   * @gotag: validate:"required"
   *
   * @generated from field: int64 id = 1;
   */
  id: bigint;

  /**
   * @gotag: validate:"required"
   *
   * @generated from field: int32 price = 3;
   */
  price: number;

  /**
   * @gotag: validate:"required"
   *
   * @generated from field: string description = 4;
   */
  description: string;

  /**
   * @gotag: validate:"required"
   *
   * @generated from field: int32 max_sub = 5;
   */
  maxSub: number;

  /**
   * @gotag: validate:"required"
   *
   * @generated from field: string image = 6;
   */
  image: string;

  /**
   * @gotag: validate:"required"
   *
   * @generated from field: string title = 7;
   */
  title: string;
};

/**
 * Describes the message api.campaigns.v1.UpdateCampaignVariantByIdRequest.
 * Use `create(UpdateCampaignVariantByIdRequestSchema)` to create a new message.
 */
export const UpdateCampaignVariantByIdRequestSchema: GenMessage<UpdateCampaignVariantByIdRequest> = /*@__PURE__*/
  messageDesc(file_campaigns_v1_campaignvariant, 11);

/**
 * @generated from message api.campaigns.v1.GetCampaignVariantSubsByIdRequest
 */
export type GetCampaignVariantSubsByIdRequest = Message<"api.campaigns.v1.GetCampaignVariantSubsByIdRequest"> & {
  /**
   * @gotag: validate:"required"
   *
   * @generated from field: int64 id = 1;
   */
  id: bigint;
};

/**
 * Describes the message api.campaigns.v1.GetCampaignVariantSubsByIdRequest.
 * Use `create(GetCampaignVariantSubsByIdRequestSchema)` to create a new message.
 */
export const GetCampaignVariantSubsByIdRequestSchema: GenMessage<GetCampaignVariantSubsByIdRequest> = /*@__PURE__*/
  messageDesc(file_campaigns_v1_campaignvariant, 12);

/**
 * @generated from message api.campaigns.v1.DeleteCampaignVariantByIdResponse
 */
export type DeleteCampaignVariantByIdResponse = Message<"api.campaigns.v1.DeleteCampaignVariantByIdResponse"> & {
  /**
   * @generated from field: bool success = 1;
   */
  success: boolean;

  /**
   * @generated from field: string message = 2;
   */
  message: string;
};

/**
 * Describes the message api.campaigns.v1.DeleteCampaignVariantByIdResponse.
 * Use `create(DeleteCampaignVariantByIdResponseSchema)` to create a new message.
 */
export const DeleteCampaignVariantByIdResponseSchema: GenMessage<DeleteCampaignVariantByIdResponse> = /*@__PURE__*/
  messageDesc(file_campaigns_v1_campaignvariant, 13);

/**
 * @generated from message api.campaigns.v1.UpdateCampaignVariantByIdResponse
 */
export type UpdateCampaignVariantByIdResponse = Message<"api.campaigns.v1.UpdateCampaignVariantByIdResponse"> & {
  /**
   * @generated from field: bool success = 1;
   */
  success: boolean;

  /**
   * @generated from field: string message = 2;
   */
  message: string;
};

/**
 * Describes the message api.campaigns.v1.UpdateCampaignVariantByIdResponse.
 * Use `create(UpdateCampaignVariantByIdResponseSchema)` to create a new message.
 */
export const UpdateCampaignVariantByIdResponseSchema: GenMessage<UpdateCampaignVariantByIdResponse> = /*@__PURE__*/
  messageDesc(file_campaigns_v1_campaignvariant, 14);

/**
 * @generated from service api.campaigns.v1.CampaignVariantService
 */
export const CampaignVariantService: GenService<{
  /**
   * @generated from rpc api.campaigns.v1.CampaignVariantService.AddCampaignVariant
   */
  addCampaignVariant: {
    methodKind: "unary";
    input: typeof AddCampaignVariantRequestSchema;
    output: typeof AddCampaignVariantResponseSchema;
  },
  /**
   * @generated from rpc api.campaigns.v1.CampaignVariantService.GetAllCampaignVariants
   */
  getAllCampaignVariants: {
    methodKind: "unary";
    input: typeof GetAllCampaignVariantsRequestSchema;
    output: typeof GetAllCampaignVariantsResponseSchema;
  },
  /**
   * @generated from rpc api.campaigns.v1.CampaignVariantService.GetCampaignVariantById
   */
  getCampaignVariantById: {
    methodKind: "unary";
    input: typeof GetCampaignVariantByIdRequestSchema;
    output: typeof GetCampaignVariantByIdResponseSchema;
  },
  /**
   * @generated from rpc api.campaigns.v1.CampaignVariantService.DeleteCampaignVariantById
   */
  deleteCampaignVariantById: {
    methodKind: "unary";
    input: typeof DeleteCampaignVariantByIdRequestSchema;
    output: typeof DeleteCampaignVariantByIdResponseSchema;
  },
  /**
   * @generated from rpc api.campaigns.v1.CampaignVariantService.UpdateCampaignVariantById
   */
  updateCampaignVariantById: {
    methodKind: "unary";
    input: typeof UpdateCampaignVariantByIdRequestSchema;
    output: typeof UpdateCampaignVariantByIdResponseSchema;
  },
  /**
   * @generated from rpc api.campaigns.v1.CampaignVariantService.GetCampaignVariantSubs
   */
  getCampaignVariantSubs: {
    methodKind: "unary";
    input: typeof GetCampaignSubRequestSchema;
    output: typeof GetCampaignSubResponseSchema;
  },
}> = /*@__PURE__*/
  serviceDesc(file_campaigns_v1_campaignvariant, 0);

