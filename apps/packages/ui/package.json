{"name": "@vtuber/ui", "version": "0.0.0", "type": "module", "private": true, "scripts": {"lint": "eslint . --max-warnings 0"}, "dependencies": {"@bufbuild/protobuf": "^2.5.2", "@lexical/code": "^0.24.0", "@lexical/file": "^0.24.0", "@lexical/hashtag": "^0.24.0", "@lexical/html": "^0.24.0", "@lexical/link": "^0.24.0", "@lexical/list": "^0.24.0", "@lexical/overflow": "^0.24.0", "@lexical/react": "^0.24.0", "@lexical/rich-text": "^0.24.0", "@lexical/selection": "^0.24.0", "@lexical/table": "^0.24.0", "@lexical/utils": "^0.24.0", "@radix-ui/react-accordion": "^1.2.2", "@radix-ui/react-alert-dialog": "^1.1.6", "@radix-ui/react-aspect-ratio": "^1.1.2", "@radix-ui/react-avatar": "^1.1.2", "@radix-ui/react-checkbox": "^1.1.3", "@radix-ui/react-collapsible": "^1.1.3", "@radix-ui/react-dialog": "^1.1.6", "@radix-ui/react-dropdown-menu": "^2.1.4", "@radix-ui/react-hover-card": "^1.1.10", "@radix-ui/react-icons": "^1.3.2", "@radix-ui/react-label": "^2.1.2", "@radix-ui/react-popover": "^1.1.4", "@radix-ui/react-progress": "^1.1.1", "@radix-ui/react-radio-group": "^1.2.3", "@radix-ui/react-scroll-area": "^1.2.3", "@radix-ui/react-select": "^2.1.6", "@radix-ui/react-separator": "^1.1.1", "@radix-ui/react-slider": "^1.2.3", "@radix-ui/react-slot": "^1.1.2", "@radix-ui/react-switch": "^1.1.2", "@radix-ui/react-tabs": "^1.1.2", "@radix-ui/react-toggle": "^1.1.1", "@radix-ui/react-toggle-group": "^1.1.2", "@radix-ui/react-tooltip": "^1.1.8", "@tanstack/react-query": "^5.81.2", "@tanstack/react-router": "latest", "@tanstack/react-table": "latest", "@vtuber/language": "workspace:*", "@vtuber/services": "workspace:*", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "cmdk": "^1.0.0", "date-fns": "^4.1.0", "embla-carousel-react": "^8.5.2", "input-otp": "^1.4.2", "lexical": "^0.24.0", "lodash-es": "^4.17.21", "lucide-react": "0.456.0", "motion": "^12.19.1", "react": "^19.0.0", "react-colorful": "^5.6.1", "react-day-picker": "^9.6.3", "react-dom": "^19.1.0", "react-hook-form": "^7.58.1", "recharts": "^2.15.1", "sonner": "^2.0.5", "tailwind-merge": "^2.5.4", "tailwindcss-animate": "^1.0.7"}, "devDependencies": {"@types/lodash-es": "^4.17.12", "@types/node": "^24.0.3", "@types/react": "^19.1.8", "@types/react-dom": "^19.1.6", "@vtuber/eslint-config": "workspace:*", "@vtuber/typescript-config": "workspace:*", "autoprefixer": "^10.4.21", "postcss": "^8.5.6", "react": "19.1.0", "tailwindcss": "^3.4.14", "typescript": "^5.8.3"}, "exports": {"./globals.css": "./src/styles/globals.css", "./postcss.config": "./postcss.config.mjs", "./tailwind.config": "./tailwind.config.ts", "./lib/*": "./src/lib/*.ts", "./components/*": "./src/components/*.tsx", "./form/*": "./src/components/form-inputs/*.tsx", "./hooks/*": "./src/hooks/*.tsx", "./form/creator/*": "./src/form/creator/*.tsx", "./lexical/*": "./src/lexical/*.tsx", "./constants": "./src/constants.ts"}}