import {
  Select,
  SelectContent,
  SelectItem,
  SelectProps,
  SelectTrigger,
  SelectValue,
} from "../select";

import { useLanguage } from "@vtuber/language/hooks";
import { Control, FieldValues, Path } from "react-hook-form";
import { InputProps } from "../input";
import { getInputProps, InputField } from "./input-field";

type SelectInputProps<T extends FieldValues> = Omit<SelectProps, "value"> & {
  control: Control<T>;
  name: Path<T>;
  label: string;
  isLoading?: boolean;
  placeholder?: string;
  description?: string;
  variant?: InputProps["variant"];
  size?: InputProps["size"];
  wrapperClassName?: string;
  className?: string;
  serialize?: (value: string) => unknown;
  stringify?: (value: unknown) => string;
  options: {
    label: string;
    value: string;
    icon?: React.ReactNode;
    disabled?: boolean;
  }[];
};

export const SelectInput = <T extends FieldValues>({
  isLoading,
  ...props
}: SelectInputProps<T>) => {
  const [fieldProps, inputProps] = getInputProps(props);
  const { getText } = useLanguage();

  return (
    <InputField
      {...fieldProps}
      wrapperClassName={props.wrapperClassName}
      input={({ field }) => (
        <Select
          {...inputProps}
          value={!!field?.value ? String(field.value) : undefined}
          onValueChange={(text) => {
            field.onChange(props.serialize?.(text) ?? text);
            props.onValueChange?.(text);
          }}>
          <SelectTrigger
            variant={props.variant}
            size={props.size}
            className={inputProps.className}
            id={props.name}>
            <SelectValue placeholder={inputProps.placeholder} />
          </SelectTrigger>

          <SelectContent>
            {isLoading && (
              <SelectItem
                value="loading"
                disabled>
                {getText("loading")}...
              </SelectItem>
            )}

            {!isLoading &&
              props.options.map((opt, i) => (
                <SelectItem
                  key={opt.value + i}
                  value={
                    props.stringify ? props.stringify(opt.value) : opt.value
                  }
                  disabled={opt.disabled}>
                  <div className="flex w-full justify-between">
                    {opt.icon && opt.icon}
                    {opt.label}
                  </div>
                </SelectItem>
              ))}
          </SelectContent>
        </Select>
      )}
    />
  );
};
