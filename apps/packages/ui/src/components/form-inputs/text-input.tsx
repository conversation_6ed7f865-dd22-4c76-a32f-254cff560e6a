import { FieldValues } from "react-hook-form";
import { Input, InputProps } from "../input";
import { InputField, InputFieldProps, getInputProps } from "./input-field";

type TextInputProps<T extends FieldValues> = InputProps &
  Omit<InputFieldProps<T>, "input"> & {
    isBigInt?: boolean;
  };

export const TextInput = <T extends FieldValues>(props: TextInputProps<T>) => {
  const [fieldProps, inputProps] = getInputProps(props);
  return (
    <InputField
      {...fieldProps}
      wrapperClassName={props.wrapperClassName}
      labelClassName={props.labelClassName}
      input={({ field }) => (
        <Input
          {...inputProps}
          style={inputProps.style}
          id={fieldProps.name}
          value={field.value}
          onChange={(e) => {
            if (props.type === "file") {
              props.onChange?.(e);
              field.onChange(e.target.files?.[0]);
              return;
            }
            if (props.type === "number") {
              if (props.isBigInt) field.onChange(BigInt(e.target.value));
              else field.onChange(e.target.valueAsNumber);
              return;
            }
            props.onChange?.(e);
            field.onChange(e);
          }}
        />
      )}
    />
  );
};
