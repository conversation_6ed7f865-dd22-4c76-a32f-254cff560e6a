import { createFileRoute, Link, notFound } from "@tanstack/react-router";
import { useLanguage } from "@vtuber/language/hooks";
import { vtuberPlanClient } from "@vtuber/services/client";
import { Badge } from "@vtuber/ui/components/badge";
import { Button, buttonVariants } from "@vtuber/ui/components/button";
import {
  Card,
  CardContent,
  CardFooter,
  CardHeader,
  CardTitle,
} from "@vtuber/ui/components/card";
import { cn } from "@vtuber/ui/lib/utils";
import { Edit, Eye, Plus, Trash2 } from "lucide-react";
import { DeleteDialog } from "~/components/DeleteDialog";
import { NoPlansMessage } from "~/components/plans/no-plans-message";

export const Route = createFileRoute("/_app/plans/")({
  component: RouteComponent,
  loader: async ({ context }) => {
    const [data, err] = await vtuberPlanClient.getAllVtuberPlansByVtuberId({
      vtuberId: context.user?.vtuber?.id!,
    });

    if (err) {
      throw notFound({
        data: err.rawMessage,
      });
    }

    return { data };
  },
});

function RouteComponent() {
  const { getText } = useLanguage();
  const { data } = Route.useLoaderData();

  if (!data?.VtuberPlan || data.VtuberPlan.length === 0)
    return <NoPlansMessage />;

  const plans = data.VtuberPlan;

  return (
    <div className="container pt-10">
      <div className="grid md:grid-cols-3 sm:grid-cols-2 grid-cols-1 gap-6">
        {plans.map((plan) => (
          <Card
            key={plan.id.toString()}
            className="overflow-hidden transition-all duration-200 hover:shadow-lg border-0 flex-col flex justify-between shadow-xl">
            <CardHeader className="pb-4 flex-row justify-between">
              <CardTitle className="text-2xl font-semibold text-white">
                {plan.title}
              </CardTitle>
              <Badge
                variant={"secondary"}
                className="rounded-full bg-gradient-3">
                {getText("Display_Order")}: {plan.index}
              </Badge>
            </CardHeader>

            <CardContent className="space-y-4 pt-4">
              <section className="grid grid-cols-2 items-center bg-muted p-3">
                <p className="font-medium">{getText("price")}</p>
                <p className="font-bold text-xl">
                  {plan.price} {getText("yen")}
                </p>
              </section>
              <section className="grid grid-cols-2 items-center bg-muted p-3">
                <p className="font-medium">Annual Price</p>
                <p className="font-bold text-xl">
                  {plan.annualPrice} {getText("yen")}
                </p>
              </section>
            </CardContent>
            <CardFooter>
              <div className="flex gap-2 w-full">
                <Link
                  to={"/plans/$id"}
                  params={{ id: plan.id.toString() }}
                  className={cn(
                    buttonVariants({ variant: "minimal-dark", size: "lg" }),
                    "flex-1 ",
                  )}>
                  <Eye className="size-4" />
                  {getText("view")}
                </Link>

                <Link
                  to={`/plans/$id/edit`}
                  params={{ id: plan.id.toString() }}
                  className={cn(
                    buttonVariants({ variant: "minimal-dark", size: "lg" }),
                    "flex-1 ",
                  )}>
                  <Edit className="size-4" />
                  {getText("EDIT")}
                </Link>
                <DeleteDialog
                  name={plan.title}
                  onDelete={() =>
                    vtuberPlanClient.deleteVtuberPlanById({
                      id: plan.id,
                    })
                  }>
                  <Button
                    variant="muted-destructive"
                    size={"lg"}>
                    <Trash2 className="w-4 h-4" />
                  </Button>
                </DeleteDialog>
              </div>
            </CardFooter>
          </Card>
        ))}
        {data.VtuberPlan.length < 3 && (
          <Link
            to="/plans/add"
            className="block">
            <Card className="bg-slate-800 h-full border-slate-700 border-dashed border-2 hover:border-orange-500 transition-colors cursor-pointer group">
              <CardContent className="p-6 h-full flex flex-col items-center justify-center text-center">
                <div className="w-16 h-16 bg-tertiary rounded-full flex items-center justify-center mb-4 group-hover:bg-tertiary transition-colors">
                  <Plus className="w-8 h-8 text-white" />
                </div>
                <h3 className="text-lg font-semibold text-white mb-2">
                  Add New Item
                </h3>
                <p className="text-slate-400 text-sm">
                  Click to create a new item
                </p>
              </CardContent>
            </Card>
          </Link>
        )}
      </div>
    </div>
  );
}
