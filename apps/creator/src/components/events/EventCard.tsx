import { timestampDate } from "@bufbuild/protobuf/wkt";
import { useMutation } from "@connectrpc/connect-query";
import { Link } from "@tanstack/react-router";
import {
  useAuth,
  useCategories,
  useCreatorParticipatedEvents,
} from "@vtuber/auth/hooks";
import { useLanguage } from "@vtuber/language/hooks";
import { eventClient } from "@vtuber/services/client";
import { Event, EventParticipantService } from "@vtuber/services/events";
import { AspectRatio } from "@vtuber/ui/components/aspect-ratio";
import { Avatar } from "@vtuber/ui/components/avatar";
import { Badge } from "@vtuber/ui/components/badge";
import { Button, buttonVariants } from "@vtuber/ui/components/button";
import { Card, CardContent, CardFooter } from "@vtuber/ui/components/card";
import { Spinner } from "@vtuber/ui/components/spinner";
import { Tag } from "@vtuber/ui/components/tag";
import { cn, getRemainingDays } from "@vtuber/ui/lib/utils";
import { CheckCircle, Edit, Play, Trash } from "lucide-react";
import { toast } from "sonner";
import { DeleteDialog } from "../DeleteDialog";
import { RemainingDaysBadge } from "../remaining-days-badge";
import { EventStatus } from "./event-status";

interface Props {
  event: Event;
  showAdminControls?: boolean;
  isPartication?: boolean;
}

export const EventCard = ({
  event,
  showAdminControls = true,
  isPartication = false,
}: Props) => {
  const daysLeft = getRemainingDays(event.startDate!, event.endDate!);
  const { getMultipleCategories } = useCategories();
  const { creatorParticipation, setCreatorParticipation } =
    useCreatorParticipatedEvents();
  const addMutation = useMutation(
    EventParticipantService.method.addEventParticipation,
    {
      onSuccess(data) {
        const creatorParticipations = [...creatorParticipation, event.id];
        setCreatorParticipation(creatorParticipations);
        toast.success(data.message);
      },
      onError(err) {
        toast.error(err.message);
      },
    },
  );

  const addEventParticipation = (eventId: bigint) => {
    addMutation.mutate({
      eventId,
    });
  };
  const hasParticipated = creatorParticipation?.includes(event.id);
  const { getText } = useLanguage();
  const { session: user } = useAuth();
  const author = event.vtuber || event.user;
  const createdBy = event.vtuber ? getText("Vtuber") : getText("User");
  const tags = getMultipleCategories(event.categories).filter(Boolean);

  const showControls = showAdminControls && event.user?.id === user?.user?.id;

  return (
    <Card className="bg-slate-800 border-slate-600 overflow-hidden hover:border-slate-500 transition-all group flex flex-col justify-between">
      <Link
        to="/event/$id"
        params={{
          id: event.id.toString(),
        }}
        className="relative">
        <AspectRatio
          ratio={19 / 9}
          className=" overflow-hidden bg-slate-900">
          <img
            src={event.image}
            alt={event.title}
            className="w-full h-full object-cover group-hover:scale-105 transition-transform duration-300"
          />
        </AspectRatio>

        <div className="absolute top-3 left-0 px-3 flex w-full justify-between">
          <EventStatus status={event.status} />
          <RemainingDaysBadge
            startDate={event.startDate!}
            endDate={event.endDate!}
          />
        </div>
        <Badge
          className="absolute bottom-3 left-3 bg-slate-900/90 backdrop-blur-sm rounded-full hover:bg-slate-900 text-slate-100 border-slate-600"
          variant="outline">
          {timestampDate(event.endDate!).toLocaleDateString()}
        </Badge>
      </Link>
      <Link
        to="/event/$id"
        params={{ id: event.id.toString() }}>
        <CardContent className="p-5">
          <h3 className="text-xl font-bold mb-2 line-clamp-1 text-font capitalize">
            {event.title}
          </h3>
          <p className="text-slate-300 text-sm mb-4 line-clamp-2">
            {event.shortDescription}
          </p>

          <div className="flex items-center gap-2 mb-4">
            <Avatar
              className="size-8 [&>svg]:!size-4"
              src={author?.image}
              fallback={author?.name}
              alt={author?.name}
            />

            <span className="text-sm text-slate-300">{author?.name}</span>
            <Badge
              variant="outline"
              className="text-xs rounded-full">
              {createdBy}
            </Badge>
            {showControls && (
              <Badge
                variant="info"
                className="rounded-full text-xs">
                owner
              </Badge>
            )}
          </div>

          {tags.length > 0 && (
            <div className="flex items-center gap-3 flex-wrap">
              {tags.map((t) => (
                <Tag
                  key={t?.id}
                  variant={"outline-white"}
                  className="text-xs">
                  {t?.name}
                </Tag>
              ))}
            </div>
          )}
        </CardContent>
      </Link>
      {!isPartication && (
        <CardFooter className="p-5 flex gap-2">
          <Button
            size={"lg"}
            onClick={() => {
              if (hasParticipated) return;
              addEventParticipation(event.id);
            }}
            variant={hasParticipated ? "secondary" : "success"}
            className={"flex-1 font-medium rounded-lg"}
            disabled={
              hasParticipated || daysLeft <= 0 || addMutation.isPending
            }>
            {hasParticipated ? (
              <div className="flex items-center">
                <CheckCircle className="w-4 h-4 mr-2" />
                Participated
              </div>
            ) : daysLeft <= 0 ? (
              "Campaign Ended"
            ) : (
              <div className="flex items-center">
                {addMutation.isPending ? (
                  <Spinner className="mr-2 !size-8" />
                ) : (
                  <Play className="w-4 h-4 mr-2" />
                )}
                Participate
              </div>
            )}
          </Button>
          {showControls && (
            <Link
              to="/event/$id/edit"
              params={{
                id: event.id.toString(),
              }}
              className={cn(
                buttonVariants({
                  variant: "muted",
                  size: "lg",
                }),
                "rounded-lg px-5",
              )}>
              <Edit className="w-4 h-4 mr-2" />
              Edit
            </Link>
          )}
          {showControls && (
            <DeleteDialog
              name={event.title}
              onDelete={() => eventClient.deleteEventById({ id: event.id })}>
              <Button
                variant="muted"
                size={"lg"}
                className="rounded-lg px-5">
                <Trash className="w-4 h-4 mr-2" />
                Delete
              </Button>
            </DeleteDialog>
          )}
        </CardFooter>
      )}
    </Card>
  );
};
