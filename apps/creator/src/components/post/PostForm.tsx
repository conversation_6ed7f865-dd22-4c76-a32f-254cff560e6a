import { useMutation, useQuery } from "@connectrpc/connect-query";
import { useNavigate, useRouter } from "@tanstack/react-router";
import { useCategories } from "@vtuber/auth/hooks";
import { useLanguage } from "@vtuber/language/hooks";
import { CampaignService } from "@vtuber/services/campaigns";
import { handleConnectError, OmitTypeName } from "@vtuber/services/client";
import {
  AddPostRequest,
  PostService,
  UpdatePostByIdRequest,
} from "@vtuber/services/content";
import { Button } from "@vtuber/ui/components/button";
import {
  Card,
  CardContent,
  CardHeader,
  CardTitle,
} from "@vtuber/ui/components/card";
import { Form } from "@vtuber/ui/components/form";
import { FileInput } from "@vtuber/ui/components/form-inputs/file-input";
import { HtmlInput } from "@vtuber/ui/components/form-inputs/html-input";
import { SelectInput } from "@vtuber/ui/components/form-inputs/select-input";
import { SwitchInput } from "@vtuber/ui/components/form-inputs/switch-input";
import { TextAreaInput } from "@vtuber/ui/components/form-inputs/text-area-input";
import { TextInput } from "@vtuber/ui/components/form-inputs/text-input";
import { Label } from "@vtuber/ui/components/label";
import { useGetMediaTypes } from "@vtuber/ui/hooks/get-media-types";
import {
  FileText,
  Globe,
  ImageIcon,
  Lock,
  Save,
  Settings,
  Type,
} from "lucide-react";
import { useForm } from "react-hook-form";
import { toast } from "sonner";

type PostFormProps = {
  mode: "create" | "edit";
  defaultValues?: OmitTypeName<AddPostRequest> & { id?: bigint };
  onSuccess?: () => void;
};

export function PostForm({ mode, defaultValues, onSuccess }: PostFormProps) {
  const { categories } = useCategories();
  const navigate = useNavigate();
  const router = useRouter();
  const { getText } = useLanguage();
  const { mediaOptions } = useGetMediaTypes();
  const { data: campaigns, isPending: LoadingCampaigns } = useQuery(
    CampaignService.method.getMyCampaignNames,
    {},
  );

  const form = useForm<OmitTypeName<AddPostRequest | UpdatePostByIdRequest>>({
    defaultValues: {
      categoryId: defaultValues?.categoryId,
      name: defaultValues?.name,
      description: defaultValues?.description,
      shortDescription: defaultValues?.shortDescription,
      title: defaultValues?.title,
      media: defaultValues?.media,
      mediaType: defaultValues?.mediaType || "picture",
      membershipOnly: true,
      campaignId: defaultValues?.campaignId,
    },
  });
  const mediaType = form.watch("mediaType");

  const createMutation = useMutation(PostService.method.addPost, {
    onError: (err) => {
      handleConnectError(err, form);
    },
    onSuccess: () => {
      toast.success("Post created successfully");
      router.invalidate();
      onSuccess?.();
      router.navigate({
        to: "/posts",
      });
    },
  });

  const updateMutation = useMutation(PostService.method.updatePostById, {
    onError: (err) => {
      handleConnectError(err, form);
    },
    onSuccess: () => {
      toast.success("Post updated successfully");
      router.invalidate();
      onSuccess?.();
      navigate({ to: "/posts" });
    },
  });

  const onSubmit = form.handleSubmit((val) => {
    console.log(val);
    if (mode === "create") {
      createMutation.mutateAsync(val);
    } else {
      updateMutation.mutateAsync({
        ...val,
        id: defaultValues?.id ?? 0n,
        categoryId: BigInt(val.categoryId ?? 0n),
      });
    }
  });

  const isPending = createMutation.isPending || updateMutation.isPending;
  const memberShiponly = form.watch("membershipOnly");

  return (
    <div className="mt-10 md:max-w-4xl w-full mx-auto">
      <Form {...form}>
        <form
          onSubmit={onSubmit}
          className="space-y-6">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2 text-lg">
                <Type className="w-5 h-5 mt-1" />
                Basic Information
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-6">
              <TextInput
                wrapperClassName="space-y-2"
                size={"lg"}
                variant={"muted"}
                control={form.control}
                name="title"
                label={getText("Post_Title") + "*"}
                placeholder="Title"
              />
              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                <TextInput
                  variant={"muted"}
                  wrapperClassName="space-y-2"
                  size={"lg"}
                  control={form.control}
                  name="name"
                  label={getText("Post_Name")}
                  placeholder={getText("Post_Name")}
                />
                <SelectInput
                  wrapperClassName="space-y-2"
                  variant={"muted"}
                  size={"lg"}
                  placeholder="Select a category"
                  control={form.control}
                  serialize={(val) => BigInt(val)}
                  label={getText("category") + "*"}
                  name="categoryId"
                  options={categories.map((category) => ({
                    value: category.id.toString(),
                    label: category.name,
                  }))}
                />
              </div>
              <SelectInput
                label="Campaign"
                wrapperClassName="space-y-2"
                variant={"muted"}
                size={"lg"}
                isLoading={LoadingCampaigns}
                placeholder="Select a campaign"
                control={form.control}
                name="campaignId"
                options={
                  campaigns?.data.map((c) => ({
                    label: c.name,
                    value: c.id.toString(),
                  })) || []
                }
              />
              <TextAreaInput
                variant={"muted"}
                wrapperClassName="space-y-2"
                control={form.control}
                name="shortDescription"
                label={getText("short_description") + "*"}
                placeholder={getText("short_description")}
                maxLength={250}
              />
            </CardContent>
          </Card>

          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2 text-lg">
                <FileText className="w-5 h-5" />
                {getText("description")}
              </CardTitle>
            </CardHeader>
            <CardContent>
              <HtmlInput
                control={form.control}
                name="description"
              />
            </CardContent>
          </Card>
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2 text-lg">
                <ImageIcon className="w-5 h-5 mt-1" />
                {getText("Media")}
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-6">
              <SelectInput
                variant={"muted"}
                size={"lg"}
                options={mediaOptions}
                control={form.control}
                name="mediaType"
                label={getText("Media_Type")}
              />

              <FileInput
                mediaType={mediaType}
                control={form.control}
                name="media"
                label={mediaType === "video" ? "Upload video" : "Upload image"}
                defaultImage={
                  mode === "edit" ? defaultValues?.media : undefined
                }
              />
            </CardContent>
          </Card>

          <Card>
            <CardHeader>
              <CardTitle className="text-lg flex items-center gap-2">
                <Settings className="size-5 mt-1" /> Settings
              </CardTitle>
            </CardHeader>
            <CardContent className="relative overflow-hidden">
              <Label
                className="absolute inset-0"
                htmlFor="membershipOnly"
              />
              <div className="flex items-center justify-between">
                <div className="space-y-1">
                  <div className="flex items-center gap-2">
                    {memberShiponly ? (
                      <Lock className="w-4 h-4 text-green-600" />
                    ) : (
                      <Globe className="w-4 h-4 text-blue-600" />
                    )}
                    <p className="font-medium">
                      {memberShiponly ? "Membership Only" : "Public Post"}
                    </p>
                  </div>
                  <p className="text-sm text-slate-500">
                    {memberShiponly
                      ? "Only members can view this post"
                      : "Anyone can view this post"}
                  </p>
                </div>
                <SwitchInput
                  size="lg"
                  id="membershipOnly"
                  name="membershipOnly"
                  control={form.control}
                />
              </div>
            </CardContent>
          </Card>
          <Button
            variant={"success"}
            loading={isPending}
            size={"xl"}
            className="w-full">
            <Save className="w-5 h-5 mr-2" />
            {getText(mode === "create" ? "Create_Post" : "Update_Post")}
          </Button>
        </form>
      </Form>
    </div>
  );
}
