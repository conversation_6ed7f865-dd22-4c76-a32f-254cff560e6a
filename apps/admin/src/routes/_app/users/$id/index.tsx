import { createFileRoute, notFound } from "@tanstack/react-router";
import { useLanguage } from "@vtuber/language/hooks";
import { postsClient, usersClient } from "@vtuber/services/client";
import { Badge } from "@vtuber/ui/components/badge";
import {
  Card,
  CardContent,
  CardFooter,
  CardHeader,
} from "@vtuber/ui/components/card";
import { Media } from "@vtuber/ui/components/media";
import { Ban, CheckCircle, Mail, Shield } from "lucide-react";
import { useState } from "react";
import { UserPosts } from "~/components/UserPosts";
import { VtuberProfileDialog } from "~/components/VtuberDialog";

export const Route = createFileRoute("/_app/users/$id/")({
  component: RouteComponent,
  loader: async ({ params }) => {
    const [user] = await usersClient.getUserById({
      id: BigInt(params.id),
    });
    const [posts] = await postsClient.getAllPosts({
      vtuberId: user?.data?.vtuber?.id,
    });

    if (!user) {
      throw notFound({
        data: "User not found",
      });
    }
    return { user, posts };
  },
});

function RouteComponent() {
  const { user, posts } = Route.useLoaderData();
  const { getText } = useLanguage();
  const [open, setOpen] = useState(false);
  return (
    <>
      <Card>
        <CardHeader className="">
          <div className="absolute  rounded-full shadow-md">
            <Media
              src={user.data?.image}
              alt={user.data?.fullName}
              className="w-24 h-24 rounded-full object-cover"
            />
          </div>
        </CardHeader>
        <CardContent className="pt-6 mt-10">
          <h1 className="text-2xl font-bold ">{user.data?.fullName}</h1>
          <div className="flex items-center  mt-2">
            <Mail
              size={16}
              className="mr-2"
            />
            <span>{user.data?.email}</span>
          </div>
          <div className="flex flex-wrap gap-2 mt-6">
            {user.data?.emailVerified && (
              <StatusBadge
                icon={CheckCircle}
                label="Email Verified"
                variant="default"
              />
            )}
            {user.data?.isVtuber && (
              <StatusBadge
                icon={Shield}
                label="Vtuber"
                variant="success"
              />
            )}
            {user.data?.role == "user" && (
              <StatusBadge
                icon={Shield}
                label="User"
                variant="success"
              />
            )}
            {user.data?.isBanned && (
              <StatusBadge
                icon={Ban}
                label="Banned"
                variant="destructive"
              />
            )}
          </div>
        </CardContent>
        <CardFooter className="flex justify-end gap-3 border-t  pt-4">
          {user.data?.isVtuber ? (
            <VtuberProfileDialog
              id={user.data?.vtuber?.id ?? BigInt(0)}
              open={open}
              setOpen={setOpen}
            />
          ) : (
            <div className="text-sm">{getText("No_Profile")}</div>
          )}
        </CardFooter>
      </Card>
      <h3 className="text-3xl mt-8">{getText("Recent_Posts")}</h3>
      {posts?.data?.length ? (
        <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-6 mt-4">
          {posts.data.map((post) => (
            <UserPosts
              key={post.id}
              post={post}
            />
          ))}
        </div>
      ) : (
        <div className="mt-4 p-8 text-center border rounded-lg bg-muted/50">
          <p className="text-muted-foreground">
            {getText("no_posts_for_this_vtuber")}
          </p>
        </div>
      )}
    </>
  );
}

const StatusBadge = ({
  icon: Icon,
  label,
  variant,
}: {
  icon: any;
  label: string;
  variant: "default" | "success" | "destructive";
}) => {
  return (
    <Badge
      variant={variant}
      className="flex items-center gap-2 py-1">
      <Icon size={16} />
      <span>{label}</span>
    </Badge>
  );
};
