import { AppSidebarProps } from "@vtuber/ui/components/app-sidebar";
import {
  Building,
  FileText,
  InfoIcon,
  Scale,
  Share2,
  ShieldCheck,
  Users,
} from "lucide-react";

export const staticResources: AppSidebarProps["links"] = [
  {
    url: "/privacy-policy",
    title: "privacy_policy",
    icon: ShieldCheck,
  },
  {
    url: "/terms-condition",
    title: "terms_of_use",
    icon: FileText,
  },
  {
    url: "/transaction-act",
    title: "transaction_act",
    icon: Scale,
  },
  {
    url: "/crowdfunding-guidelines",
    title: "crowdfunding_guidelines",
    icon: InfoIcon,
  },
  {
    url: "/community-guidelines",
    title: "community_guidelines",
    icon: Users,
  },
  {
    url: "/operating-company",
    title: "operating_company",
    icon: Building,
  },
  {
    url: "/social-links",
    title: "Social_Links",
    icon: Share2,
  },
];
